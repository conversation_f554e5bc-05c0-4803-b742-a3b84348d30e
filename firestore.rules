rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions
    function isSignedIn() {
      return request.auth != null;
    }

    function isAdmin() {
      return isSignedIn() &&
             exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
             get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }

    function isManager() {
      return isSignedIn() &&
             exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
             get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'manager';
    }

    function isAdminOrManager() {
      return isAdmin() || isManager();
    }

    // SECURE RULES WITH PUBLIC ACCESS FOR LOGIN

    // Users collection - needed for authentication
    match /users/{userId} {
      // Allow reading user documents for authentication
      allow read: if true;
      // Only admins can create new users
      allow create: if isAdmin() || !exists(/databases/$(database)/documents/users);
      // Users can update their own non-critical fields, admins can update any user
      allow update: if isSignedIn() &&
                     ((request.auth.uid == userId &&
                       request.resource.data.role == resource.data.role) ||
                      isAdmin());
      // Only admins can delete users
      allow delete: if isAdmin();
    }

    // Products collection - public read for product display
    match /products/{productId} {
      // Anyone can read products
      allow read: if true;
      // Only admins can write products
      allow write: if isAdmin();
    }

    // Categories collection - public read for product filtering
    match /categories/{categoryId} {
      // Anyone can read categories
      allow read: if true;
      // Only admins can write categories
      allow write: if isAdmin();
    }

    // Coupons collection - public read for coupon application
    match /coupons/{couponId} {
      // Anyone can read coupons
      allow read: if true;
      // Only admins can write coupons
      allow write: if isAdmin();
    }

    // System settings collection - needed for app initialization
    match /systemSettings/{settingId} {
      // Anyone can read system settings
      allow read: if true;
      // Only admins can write settings
      allow write: if isAdmin();
    }

    // Bookings collection - restricted to authenticated users
    match /bookings/{bookingId} {
      // Only authenticated users can read bookings
      allow read: if isSignedIn();
      // Only authenticated admins or managers can create bookings
      allow create: if isAdminOrManager();
      // Only admins can update or delete bookings
      allow update, delete: if isAdmin();
    }

    // Clients collection - restricted to authenticated users
    match /clients/{clientId} {
      // Only authenticated users can read clients
      allow read: if isSignedIn();
      // Only authenticated admins or managers can create clients
      allow create: if isAdminOrManager();
      // Only admins can update or delete clients
      allow update, delete: if isAdmin();
    }

    // Client activities collection - restricted to authenticated users
    match /clientActivities/{activityId} {
      // Only authenticated users can read client activities
      allow read: if isSignedIn();
      // Only authenticated admins or managers can create activities
      allow create: if isAdminOrManager();
      // Only admins can update or delete activities
      allow update, delete: if isAdmin();
    }

    // Vendors collection - restricted to authenticated users
    match /vendors/{vendorId} {
      // Only authenticated users can read vendors
      allow read: if isSignedIn();
      // Only admins can write vendors
      allow write: if isAdmin();
    }

    // Vendor transactions collection - restricted to authenticated users
    match /vendorTransactions/{transactionId} {
      // Only authenticated users can read vendor transactions
      allow read: if isSignedIn();
      // Only admins can write vendor transactions
      allow write: if isAdmin();
    }

    // Default rule - deny everything else
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
