import express from 'express';
import cors from 'cors';
import mysql from 'mysql2/promise';

const app = express();
const PORT = 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Database configuration
const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'root',
  password: '',
  database: 'teamwork_app',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
};

// Create connection pool
const pool = mysql.createPool(dbConfig);

// Helper functions
const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

// Test database connection
app.get('/api/test', async (req, res) => {
  try {
    const connection = await pool.getConnection();
    await connection.ping();
    connection.release();
    res.json({ success: true, message: 'Database connection successful' });
  } catch (error) {
    console.error('Database connection failed:', error);
    res.status(500).json({ success: false, message: 'Database connection failed' });
  }
});

// Authentication
app.post('/api/auth/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ error: 'Username and password are required' });
    }

    // Try username first
    let [users] = await pool.execute(
      'SELECT * FROM users WHERE username = ? AND password = ? AND is_active = true',
      [username, password]
    );

    // If not found, try email
    if (users.length === 0) {
      [users] = await pool.execute(
        'SELECT * FROM users WHERE email = ? AND password = ? AND is_active = true',
        [username.toLowerCase(), password]
      );
    }

    if (users.length > 0) {
      const user = users[0];
      
      // Update last login
      await pool.execute('UPDATE users SET last_login = NOW() WHERE id = ?', [user.id]);
      
      res.json({
        id: user.id,
        email: user.email,
        username: user.username,
        name: user.name,
        role: user.role,
        createdAt: user.created_at,
        lastLogin: new Date().toISOString(),
        isActive: Boolean(user.is_active)
      });
    } else {
      res.status(401).json({ error: 'Invalid username/email or password' });
    }
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Products
app.get('/api/products', async (req, res) => {
  try {
    const [rows] = await pool.execute('SELECT * FROM products ORDER BY name');
    const products = rows.map(row => ({
      id: row.id,
      name: row.name,
      category: row.category,
      sku: row.sku,
      barcode: row.barcode,
      dailyRate: parseFloat(row.daily_rate),
      weeklyRate: row.weekly_rate ? parseFloat(row.weekly_rate) : undefined,
      image: row.image,
      description: row.description,
      available: Boolean(row.available),
      quantity: row.quantity,
      stock: row.stock,
      featured: Boolean(row.featured),
      customDays: row.custom_days,
      customPrice: row.custom_price ? parseFloat(row.custom_price) : undefined,
      temporaryDailyRate: row.temporary_daily_rate ? parseFloat(row.temporary_daily_rate) : undefined,
      temporaryWeeklyRate: row.temporary_weekly_rate ? parseFloat(row.temporary_weekly_rate) : undefined,
      isExternalVendorItem: Boolean(row.is_external_vendor_item),
      vendorId: row.vendor_id,
      vendorSku: row.vendor_sku,
      vendorCost: row.vendor_cost ? parseFloat(row.vendor_cost) : undefined,
      profitMargin: row.profit_margin ? parseFloat(row.profit_margin) : undefined
    }));
    res.json(products);
  } catch (error) {
    console.error('Error fetching products:', error);
    res.status(500).json({ error: 'Failed to fetch products' });
  }
});

app.post('/api/products', async (req, res) => {
  try {
    const product = req.body;
    const id = generateId();
    
    await pool.execute(`
      INSERT INTO products (
        id, name, category, sku, barcode, daily_rate, weekly_rate, image, description,
        available, quantity, stock, featured, custom_days, custom_price,
        temporary_daily_rate, temporary_weekly_rate, is_external_vendor_item,
        vendor_id, vendor_sku, vendor_cost, profit_margin
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      id, product.name, product.category, product.sku, product.barcode,
      product.dailyRate, product.weeklyRate, product.image, product.description,
      product.available, product.quantity, product.stock, product.featured,
      product.customDays, product.customPrice, product.temporaryDailyRate,
      product.temporaryWeeklyRate, product.isExternalVendorItem, product.vendorId,
      product.vendorSku, product.vendorCost, product.profitMargin
    ]);
    
    res.json({ id });
  } catch (error) {
    console.error('Error adding product:', error);
    res.status(500).json({ error: 'Failed to add product' });
  }
});

// Categories
app.get('/api/categories', async (req, res) => {
  try {
    const [rows] = await pool.execute('SELECT DISTINCT category FROM products ORDER BY category');
    const categories = rows.map(row => row.category);
    res.json(categories);
  } catch (error) {
    console.error('Error fetching categories:', error);
    res.status(500).json({ error: 'Failed to fetch categories' });
  }
});

// Bookings
app.get('/api/bookings', async (req, res) => {
  try {
    const [rows] = await pool.execute(`
      SELECT b.*, 
             GROUP_CONCAT(
               CONCAT(bp.product_id, ':', bp.quantity, ':', bp.daily_rate, ':', bp.weekly_rate, ':', bp.total_price)
               SEPARATOR '|'
             ) as products_data
      FROM bookings b
      LEFT JOIN booking_products bp ON b.id = bp.booking_id
      GROUP BY b.id
      ORDER BY b.date DESC
    `);
    
    const bookings = rows.map(row => ({
      id: row.id,
      quoteNumber: row.quote_number,
      date: row.date.toISOString(),
      customer: {
        name: row.customer_name,
        email: row.customer_email,
        phone: row.customer_phone,
        address: row.customer_address
      },
      products: row.products_data ? row.products_data.split('|').map(productData => {
        const [productId, quantity, dailyRate, weeklyRate, totalPrice] = productData.split(':');
        return {
          id: productId,
          quantity: parseInt(quantity),
          dailyRate: parseFloat(dailyRate),
          weeklyRate: weeklyRate !== 'null' ? parseFloat(weeklyRate) : undefined,
          totalPrice: parseFloat(totalPrice)
        };
      }) : [],
      rentalPeriod: {
        dates: JSON.parse(row.rental_dates),
        days: row.rental_days,
        rentalType: row.rental_type
      },
      status: row.status,
      subtotal: parseFloat(row.subtotal),
      deliveryFee: parseFloat(row.delivery_fee),
      discount: parseFloat(row.discount),
      tax: parseFloat(row.tax),
      total: parseFloat(row.total),
      paidAmount: row.paid_amount ? parseFloat(row.paid_amount) : 0,
      remainingAmount: row.remaining_amount ? parseFloat(row.remaining_amount) : 0
    }));
    
    res.json(bookings);
  } catch (error) {
    console.error('Error fetching bookings:', error);
    res.status(500).json({ error: 'Failed to fetch bookings' });
  }
});

app.post('/api/bookings', async (req, res) => {
  try {
    const booking = req.body;
    const id = booking.quoteNumber || generateId();
    
    // Insert booking
    await pool.execute(`
      INSERT INTO bookings (
        id, quote_number, date, customer_name, customer_email, customer_phone, customer_address,
        rental_dates, rental_days, rental_type, status, subtotal, delivery_fee, discount,
        tax, total, paid_amount, remaining_amount, delivery_option_id, coupon_id
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      id, booking.quoteNumber, new Date(booking.date), booking.customer.name,
      booking.customer.email, booking.customer.phone, booking.customer.address,
      JSON.stringify(booking.rentalPeriod.dates), booking.rentalPeriod.days,
      booking.rentalPeriod.rentalType, booking.status, booking.subtotal,
      booking.deliveryFee, booking.discount, booking.tax, booking.total,
      booking.paidAmount || 0, booking.remainingAmount || 0,
      booking.delivery?.option?.id, booking.coupon?.id
    ]);
    
    // Insert booking products
    for (const product of booking.products) {
      await pool.execute(`
        INSERT INTO booking_products (booking_id, product_id, quantity, daily_rate, weekly_rate, total_price)
        VALUES (?, ?, ?, ?, ?, ?)
      `, [id, product.id, product.quantity, product.dailyRate, product.weeklyRate, product.totalPrice]);
    }
    
    res.json({ id });
  } catch (error) {
    console.error('Error adding booking:', error);
    res.status(500).json({ error: 'Failed to add booking' });
  }
});

// System Settings
app.get('/api/system-settings', async (req, res) => {
  try {
    const [rows] = await pool.execute('SELECT * FROM system_settings WHERE id = ?', ['general']);
    
    if (rows.length === 0) {
      // Return default settings
      const defaultSettings = {
        taxRate: 0,
        enableTax: false,
        deliveryOptions: [],
        lastQuoteNumber: 0
      };
      res.json(defaultSettings);
    } else {
      const result = rows[0];
      const settingsData = result.settings_data ? JSON.parse(result.settings_data) : {};
      
      res.json({
        taxRate: parseFloat(result.tax_rate) || 0,
        enableTax: Boolean(result.enable_tax),
        lastQuoteNumber: result.last_quote_number || 0,
        deliveryOptions: settingsData.deliveryOptions || []
      });
    }
  } catch (error) {
    console.error('Error fetching system settings:', error);
    res.status(500).json({ error: 'Failed to fetch system settings' });
  }
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Server running on http://localhost:${PORT}`);
  console.log('📊 API endpoints available:');
  console.log('  - GET  /api/test');
  console.log('  - POST /api/auth/login');
  console.log('  - GET  /api/products');
  console.log('  - POST /api/products');
  console.log('  - GET  /api/categories');
  console.log('  - GET  /api/bookings');
  console.log('  - POST /api/bookings');
  console.log('  - GET  /api/system-settings');
});
