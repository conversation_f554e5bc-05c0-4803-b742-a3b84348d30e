rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // SECURE RULES FOR FIREBASE AUTHENTICATION
    // These rules require proper Firebase Authentication to be implemented
    // Replace your current rules with these once you've integrated Firebase Auth
    
    // Helper functions
    function isSignedIn() {
      return request.auth != null;
    }
    
    function isAdmin() {
      return isSignedIn() && 
             exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
             get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    function isManager() {
      return isSignedIn() && 
             exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
             get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'manager';
    }
    
    // Users collection
    match /users/{userId} {
      // Users can read their own document, admins can read any user document
      allow read: if isSignedIn() && (request.auth.uid == userId || isAdmin());
      // Only admins can create new users
      allow create: if isAdmin();
      // Users can update their own non-critical fields, admins can update any user
      allow update: if isSignedIn() && 
                     (request.auth.uid == userId && 
                      request.resource.data.role == resource.data.role) || 
                     isAdmin();
      // Only admins can delete users
      allow delete: if isAdmin();
    }
    
    // Products collection
    match /products/{productId} {
      // Anyone can read products
      allow read: if true;
      // Only authenticated users with admin role can write
      allow write: if isAdmin();
    }
    
    // Bookings collection
    match /bookings/{bookingId} {
      // Only authenticated users can read bookings
      allow read: if isSignedIn();
      // Any authenticated user can create bookings
      allow create: if isSignedIn();
      // Only admins can update or delete bookings
      allow update, delete: if isAdmin();
    }
    
    // Clients collection
    match /clients/{clientId} {
      // Only authenticated users can read clients
      allow read: if isSignedIn();
      // Any authenticated user can create clients
      allow create: if isSignedIn();
      // Only admins can update or delete clients
      allow update, delete: if isAdmin();
    }
    
    // Client activities collection
    match /clientActivities/{activityId} {
      // Only authenticated users can read client activities
      allow read: if isSignedIn();
      // Any authenticated user can create activities
      allow create: if isSignedIn();
      // Only admins can update or delete activities
      allow update, delete: if isAdmin();
    }
    
    // Coupons collection
    match /coupons/{couponId} {
      // Anyone can read coupons
      allow read: if true;
      // Only admins can write coupons
      allow write: if isAdmin();
    }
    
    // Categories collection
    match /categories/{categoryId} {
      // Anyone can read categories
      allow read: if true;
      // Only admins can write categories
      allow write: if isAdmin();
    }
    
    // System settings collection
    match /systemSettings/{settingId} {
      // Only authenticated users can read settings
      allow read: if isSignedIn();
      // Only admins can write settings
      allow write: if isAdmin();
    }
    
    // Vendors collection
    match /vendors/{vendorId} {
      // Only authenticated users can read vendors
      allow read: if isSignedIn();
      // Only admins can write vendors
      allow write: if isAdmin();
    }
    
    // Vendor transactions collection
    match /vendorTransactions/{transactionId} {
      // Only authenticated users can read vendor transactions
      allow read: if isSignedIn();
      // Only admins can write vendor transactions
      allow write: if isAdmin();
    }
    
    // Default rule - deny everything else
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
