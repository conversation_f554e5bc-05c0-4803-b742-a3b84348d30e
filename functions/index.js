/**
 * Cloud Functions for Firebase - User Management and Notifications
 */

const { onCall } = require("firebase-functions/v2/https");
const { onDocumentCreated } = require("firebase-functions/v2/firestore");
const logger = require("firebase-functions/logger");
const admin = require("firebase-admin");

// Initialize Firebase Admin SDK
admin.initializeApp();

/**
 * Cloud Function to create a new user in Firebase Authentication
 * This function can only be called by authenticated users with admin role
 */
exports.createUser = onCall(async (request) => {
  // Check if the caller is authenticated
  if (!request.auth) {
    throw new Error('Unauthorized: Authentication required');
  }

  // Get the caller's UID
  const callerUid = request.auth.uid;

  try {
    // Check if the caller is an admin
    const callerDoc = await admin.firestore().collection('users').doc(callerUid).get();

    if (!callerDoc.exists || callerDoc.data().role !== 'admin') {
      throw new Error('Unauthorized: Admin privileges required');
    }

    // Get the user data from the request
    const { email, password, userData } = request.data;

    // Validate the input
    if (!email || !password || !userData) {
      throw new Error('Invalid input: email, password, and userData are required');
    }

    // Create the user in Firebase Authentication
    const userRecord = await admin.auth().createUser({
      email: email,
      password: password,
      displayName: userData.name,
    });

    // Create the user document in Firestore
    const user = {
      email: email.toLowerCase(),
      username: userData.username,
      name: userData.name,
      role: userData.role,
      createdAt: new Date().toISOString(),
      isActive: true
    };

    await admin.firestore().collection('users').doc(userRecord.uid).set(user);

    // Return the created user
    return {
      id: userRecord.uid,
      ...user
    };
  } catch (error) {
    logger.error('Error creating user:', error);
    throw new Error(`Error creating user: ${error.message}`);
  }
});

/**
 * Cloud Function to delete a user from Firebase Authentication
 * This function can only be called by authenticated users with admin role
 */
exports.deleteUser = onCall(async (request) => {
  // Check if the caller is authenticated
  if (!request.auth) {
    throw new Error('Unauthorized: Authentication required');
  }

  // Get the caller's UID
  const callerUid = request.auth.uid;

  try {
    // Check if the caller is an admin
    const callerDoc = await admin.firestore().collection('users').doc(callerUid).get();

    if (!callerDoc.exists || callerDoc.data().role !== 'admin') {
      throw new Error('Unauthorized: Admin privileges required');
    }

    // Get the user ID to delete
    const { userId } = request.data;

    if (!userId) {
      throw new Error('Invalid input: userId is required');
    }

    // Delete the user from Firebase Authentication
    await admin.auth().deleteUser(userId);

    // Delete the user document from Firestore
    await admin.firestore().collection('users').doc(userId).delete();

    return { success: true };
  } catch (error) {
    logger.error('Error deleting user:', error);
    throw new Error(`Error deleting user: ${error.message}`);
  }
});

/**
 * Cloud Function to send a notification to a specific user
 */
exports.sendNotification = onCall(async (request) => {
  // Check if the caller is authenticated
  if (!request.auth) {
    throw new Error('Unauthorized: Authentication required');
  }

  // Get the caller's UID
  const callerUid = request.auth.uid;

  try {
    // Check if the caller is an admin or manager
    const callerDoc = await admin.firestore().collection('users').doc(callerUid).get();

    if (!callerDoc.exists || (callerDoc.data().role !== 'admin' && callerDoc.data().role !== 'manager')) {
      throw new Error('Unauthorized: Admin or Manager privileges required');
    }

    // Get the notification data from the request
    const { userId, title, body, data } = request.data;

    // Validate the input
    if (!userId || !title) {
      throw new Error('Invalid input: userId and title are required');
    }

    // Get the user's FCM tokens
    const userDoc = await admin.firestore().collection('users').doc(userId).get();

    if (!userDoc.exists) {
      throw new Error(`User ${userId} not found`);
    }

    const userData = userDoc.data();
    const fcmTokens = userData.fcmTokens || [];

    if (fcmTokens.length === 0) {
      logger.info(`No FCM tokens found for user ${userId}`);
      return { success: false, message: 'No FCM tokens found for user' };
    }

    // Send the notification to each token
    const message = {
      notification: {
        title,
        body: body || ''
      },
      data: data || {},
      tokens: fcmTokens
    };

    const response = await admin.messaging().sendMulticast(message);

    logger.info(`Notification sent to user ${userId}:`, response);

    return {
      success: true,
      successCount: response.successCount,
      failureCount: response.failureCount
    };
  } catch (error) {
    logger.error('Error sending notification:', error);
    throw new Error(`Error sending notification: ${error.message}`);
  }
});

/**
 * Cloud Function to send a notification to all admin users
 */
exports.sendNotificationToAdmins = onCall(async (request) => {
  // Check if the caller is authenticated
  if (!request.auth) {
    throw new Error('Unauthorized: Authentication required');
  }

  // Get the caller's UID
  const callerUid = request.auth.uid;

  try {
    // Check if the caller is an admin or manager
    const callerDoc = await admin.firestore().collection('users').doc(callerUid).get();

    if (!callerDoc.exists || (callerDoc.data().role !== 'admin' && callerDoc.data().role !== 'manager')) {
      throw new Error('Unauthorized: Admin or Manager privileges required');
    }

    // Get the notification data from the request
    const { title, body, data } = request.data;

    // Validate the input
    if (!title) {
      throw new Error('Invalid input: title is required');
    }

    // Get all admin users
    const adminsSnapshot = await admin.firestore().collection('users')
      .where('role', '==', 'admin')
      .get();

    if (adminsSnapshot.empty) {
      logger.info('No admin users found');
      return { success: false, message: 'No admin users found' };
    }

    // Collect all FCM tokens from admin users
    const fcmTokens = [];
    adminsSnapshot.forEach(doc => {
      const userData = doc.data();
      if (userData.fcmTokens && userData.fcmTokens.length > 0) {
        fcmTokens.push(...userData.fcmTokens);
      }
    });

    if (fcmTokens.length === 0) {
      logger.info('No FCM tokens found for admin users');
      return { success: false, message: 'No FCM tokens found for admin users' };
    }

    // Send the notification to each token
    const message = {
      notification: {
        title,
        body: body || ''
      },
      data: data || {},
      tokens: fcmTokens
    };

    const response = await admin.messaging().sendMulticast(message);

    logger.info(`Notification sent to admin users:`, response);

    return {
      success: true,
      successCount: response.successCount,
      failureCount: response.failureCount
    };
  } catch (error) {
    logger.error('Error sending notification to admins:', error);
    throw new Error(`Error sending notification to admins: ${error.message}`);
  }
});

/**
 * Cloud Function triggered when a new booking is created
 * Sends a notification to all admin users
 */
exports.onNewBooking = onDocumentCreated('bookings/{bookingId}', async (event) => {
  try {
    const bookingData = event.data.data();
    const bookingId = event.params.bookingId;

    logger.info(`New booking created: ${bookingId}`);

    // Get all admin users
    const adminsSnapshot = await admin.firestore().collection('users')
      .where('role', '==', 'admin')
      .get();

    if (adminsSnapshot.empty) {
      logger.info('No admin users found');
      return;
    }

    // Collect all FCM tokens from admin users
    const fcmTokens = [];
    adminsSnapshot.forEach(doc => {
      const userData = doc.data();
      if (userData.fcmTokens && userData.fcmTokens.length > 0) {
        fcmTokens.push(...userData.fcmTokens);
      }
    });

    if (fcmTokens.length === 0) {
      logger.info('No FCM tokens found for admin users');
      return;
    }

    // Get client information
    let clientName = 'Unknown';
    if (bookingData.clientId) {
      const clientDoc = await admin.firestore().collection('clients').doc(bookingData.clientId).get();
      if (clientDoc.exists) {
        clientName = clientDoc.data().name || 'Unknown';
      }
    }

    // Send the notification to each token
    const message = {
      notification: {
        title: 'New Booking Created',
        body: `A new booking has been created for ${clientName}`
      },
      data: {
        bookingId,
        type: 'new_booking'
      },
      tokens: fcmTokens
    };

    const response = await admin.messaging().sendMulticast(message);

    logger.info(`Notification sent to admin users for new booking:`, response);
  } catch (error) {
    logger.error('Error sending notification for new booking:', error);
  }
});