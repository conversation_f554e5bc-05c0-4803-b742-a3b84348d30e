# TeamWork Rental Management System

A comprehensive rental management system built with React, TypeScript, and MySQL. This application has been migrated from Firebase to MySQL for better production deployment on Hostinger.

## 🚀 Features

- **Product Management**: Add, edit, and manage rental inventory
- **Booking System**: Create and manage customer bookings
- **Client Management**: Track customer information and history
- **Quote Generation**: Generate professional quotes with PDF export
- **Dashboard Analytics**: View business metrics and insights
- **User Authentication**: Secure login system
- **Notification System**: OneSignal integration for push notifications
- **Responsive Design**: Works on desktop and mobile devices

## 🛠 Technology Stack

- **Frontend**: React 18, TypeScript, Vite
- **Backend**: Node.js, Express
- **Database**: MySQL
- **Styling**: Tailwind CSS
- **Notifications**: OneSignal
- **Charts**: Chart.js
- **PDF Generation**: jsPDF, React-PDF

## 📋 Prerequisites

- Node.js (v16 or higher)
- MySQL (v8.0 or higher)
- npm or yarn package manager

## 🔧 Local Development Setup

### 1. Clone and Install Dependencies

```bash
git clone <your-repo-url>
cd TW_app_v4
npm install
```

### 2. Database Setup

Start MySQL service:
```bash
# On macOS with Homebrew
brew services start mysql

# On Ubuntu/Debian
sudo systemctl start mysql

# On Windows
net start mysql
```

Create database and initialize schema:
```bash
# Create database
mysql -u root -e "CREATE DATABASE IF NOT EXISTS teamwork_app;"

# Initialize database schema and default data
npm run setup-db

# Seed sample data (optional)
npm run seed-data
```

### 3. Environment Configuration

The application uses the following environment variables (already configured in `.env`):

```env
# Database Configuration
VITE_DB_HOST=localhost
VITE_DB_PORT=3306
VITE_DB_USER=root
VITE_DB_PASSWORD=
VITE_DB_NAME=teamwork_app

# Production Database (for Hostinger deployment)
VITE_PROD_DB_HOST=your_hostinger_mysql_host
VITE_PROD_DB_USER=your_hostinger_mysql_user
VITE_PROD_DB_PASSWORD=your_hostinger_mysql_password
VITE_PROD_DB_NAME=your_hostinger_mysql_database

# Environment
VITE_NODE_ENV=development
```

### 4. Start Development Server

```bash
npm run dev
```

This will start both the backend API server (port 3001) and frontend development server (port 5173).

### 5. Login Credentials

- **Username**: admin
- **Password**: defaultpassword

⚠️ **Important**: Change the default password after first login!

## 🌐 Production Deployment on Hostinger

### 1. Hostinger MySQL Database Setup

1. Log into your Hostinger control panel
2. Go to **Databases** → **MySQL Databases**
3. Create a new database:
   - Database name: `teamwork_app` (or your preferred name)
   - Username: Create a new user
   - Password: Generate a secure password
4. Note down the database host, username, password, and database name

### 2. Update Production Environment Variables

Update the production database variables in `.env`:

```env
VITE_PROD_DB_HOST=your_actual_hostinger_mysql_host
VITE_PROD_DB_USER=your_actual_hostinger_mysql_user
VITE_PROD_DB_PASSWORD=your_actual_hostinger_mysql_password
VITE_PROD_DB_NAME=your_actual_hostinger_mysql_database
VITE_NODE_ENV=production
```

### 3. Initialize Production Database

You'll need to run the database initialization on your production server. You can either:

**Option A**: Upload and run the initialization script on your server
**Option B**: Use a MySQL client to connect to your Hostinger database and run the SQL commands from `scripts/initDb.js`

### 4. Build and Deploy

```bash
# Build the application
npm run build

# The built files will be in the 'dist' directory
```

Upload the contents of the `dist` directory to your Hostinger public_html folder.

### 5. Backend API Deployment

For the backend API, you'll need to:

1. Upload the `server` directory to your Hostinger account
2. Install Node.js dependencies on the server
3. Configure the server to run the Express application
4. Update the API_BASE_URL in `src/services/database.ts` to point to your production API endpoint

## 📊 Database Schema

The application uses the following main tables:

- **products**: Rental inventory items
- **bookings**: Customer rental bookings
- **booking_products**: Many-to-many relationship between bookings and products
- **clients**: Customer information
- **users**: System users and authentication
- **coupons**: Discount coupons
- **system_settings**: Application configuration
- **vendors**: External vendor information
- **client_activities**: Customer activity tracking

## 🔐 Security Notes

1. **Change Default Password**: Always change the default admin password
2. **Database Security**: Use strong passwords for database users
3. **Environment Variables**: Never commit sensitive environment variables to version control
4. **HTTPS**: Always use HTTPS in production
5. **Input Validation**: The application includes basic input validation, but consider additional security measures for production

## 🚀 Available Scripts

- `npm run dev` - Start development server (both frontend and backend)
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run setup-db` - Initialize database schema
- `npm run seed-data` - Add sample data to database
- `npm run server` - Start backend server only
- `npm run client` - Start frontend only

## 📱 OneSignal Notifications

The application includes OneSignal integration for push notifications. The notification system is already implemented and working. No additional setup is required as you mentioned it's working perfectly.

## 🐛 Troubleshooting

### Database Connection Issues
- Ensure MySQL is running
- Check database credentials in `.env`
- Verify database exists and user has proper permissions

### API Connection Issues
- Ensure backend server is running on port 3001
- Check for CORS issues in browser console
- Verify API endpoints are accessible

### Build Issues
- Clear node_modules and reinstall: `rm -rf node_modules && npm install`
- Check for TypeScript errors: `npm run lint`

## 📞 Support

For issues or questions, please check the troubleshooting section above or create an issue in the repository.

---

**Note**: This application has been successfully migrated from Firebase to MySQL and is ready for production deployment on Hostinger.
