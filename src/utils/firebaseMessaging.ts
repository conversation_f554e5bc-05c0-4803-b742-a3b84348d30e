import { getMessaging, getToken, onMessage } from 'firebase/messaging';
import { collection, addDoc, serverTimestamp } from 'firebase/firestore';
import { db } from '../config/firebase';
import app from '../config/firebase';

// Initialize Firebase Cloud Messaging
const messaging = getMessaging(app);

// This function requests FCM token and stores it in Firestore
export const requestFCMToken = async (userId: string): Promise<string | null> => {
  try {
    // Check if permission is granted
    if (Notification.permission !== 'granted') {
      const permission = await Notification.requestPermission();
      if (permission !== 'granted') {
        console.log('Notification permission denied');
        return null;
      }
    }

    // Get FCM token
    const token = await getToken(messaging, {
      vapidKey: import.meta.env.VITE_PUBLIC_VAPID_KEY,
    });

    if (token) {
      console.log('FCM Token:', token);
      
      // Store the token in Firestore
      await saveTokenToFirestore(token, userId);
      
      return token;
    } else {
      console.log('No FCM token available');
      return null;
    }
  } catch (error) {
    console.error('Failed to get FCM token:', error);
    return null;
  }
};

// Save FCM token to Firestore
const saveTokenToFirestore = async (token: string, userId: string): Promise<void> => {
  try {
    await addDoc(collection(db, 'fcmTokens'), {
      token,
      userId,
      device: getDeviceInfo(),
      createdAt: serverTimestamp(),
      platform: 'web'
    });
    console.log('Token saved to Firestore');
  } catch (error) {
    console.error('Error saving token to Firestore:', error);
  }
};

// Get basic device info
const getDeviceInfo = () => {
  return {
    userAgent: navigator.userAgent,
    language: navigator.language,
    platform: navigator.platform,
    isIOS: /iPad|iPhone|iPod/.test(navigator.userAgent) && !(window as any).MSStream
  };
};

// Handle foreground messages
export const initForegroundMessageHandler = () => {
  onMessage(messaging, (payload) => {
    console.log('Message received in foreground:', payload);
    
    // We can display a custom notification even in foreground
    if (payload.notification) {
      const { title, body } = payload.notification;
      
      if (Notification.permission === 'granted') {
        const notification = new Notification(title as string, {
          body: body as string,
          icon: '/icons/icon-192x192.png'
        });
        
        // Handle notification click
        notification.onclick = () => {
          notification.close();
          window.focus();
          
          // If there's a specific path to navigate to
          if (payload.data && payload.data.url) {
            window.location.href = payload.data.url;
          }
        };
      }
    }
  });
};

// Function to send a notification when a new booking is made
export const sendNewBookingNotification = async (
  booking: { id: string; customerName: string; date: string; },
  userIdToNotify: string
): Promise<void> => {
  try {
    await addDoc(collection(db, 'notifications'), {
      userId: userIdToNotify,
      title: 'New Booking Confirmed',
      body: `${booking.customerName} has made a booking for ${booking.date}`,
      data: {
        bookingId: booking.id,
        url: `/bookings/${booking.id}`
      },
      read: false,
      createdAt: serverTimestamp(),
      type: 'new_booking'
    });
    
    console.log('Notification sent to Firestore for user:', userIdToNotify);
  } catch (error) {
    console.error('Error sending notification:', error);
  }
}; 