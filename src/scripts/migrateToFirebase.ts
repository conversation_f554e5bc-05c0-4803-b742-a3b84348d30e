import 'dotenv/config';
import { 
  addProduct, 
  addBooking, 
  addCoupon, 
  addClient, 
  addClientActivity, 
  updateSystemSettings 
} from '../services/firebase';
import { Product, Booking, Coupon, Client, ClientActivity, SystemSettings } from '../types';
import { products as initialProducts } from '../data/products';
import { bookings as initialBookings } from '../data/bookings';
import { coupons as initialCoupons } from '../data/coupons';
import { clients as initialClients, clientActivities as initialClientActivities } from '../data/clients';
import { systemSettings as initialSystemSettings } from '../data/systemSettings';

async function migrateData() {
  try {
    console.log('Starting migration to Firebase...');

    // Migrate products
    console.log(`Migrating ${initialProducts.length} products...`);
    for (const product of initialProducts) {
      await addProduct(product);
    }

    // Migrate bookings
    console.log(`Migrating ${initialBookings.length} bookings...`);
    for (const booking of initialBookings) {
      await addBooking(booking);
    }

    // Migrate coupons
    console.log(`Migrating ${initialCoupons.length} coupons...`);
    for (const coupon of initialCoupons) {
      await addCoupon(coupon);
    }

    // Migrate clients
    console.log(`Migrating ${initialClients.length} clients...`);
    for (const client of initialClients) {
      await addClient(client);
    }

    // Migrate client activities
    console.log(`Migrating ${initialClientActivities.length} client activities...`);
    for (const activity of initialClientActivities) {
      await addClientActivity(activity);
    }

    // Migrate system settings
    console.log('Migrating system settings...');
    await updateSystemSettings(initialSystemSettings);

    console.log('Migration completed successfully!');
  } catch (error) {
    console.error('Migration failed:', error);
  }
}

// Run migration
migrateData(); 