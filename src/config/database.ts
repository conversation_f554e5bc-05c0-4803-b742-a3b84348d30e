import mysql from 'mysql2/promise';

interface DatabaseConfig {
  host: string;
  port: number;
  user: string;
  password: string;
  database: string;
  waitForConnections: boolean;
  connectionLimit: number;
  queueLimit: number;
}

const isDevelopment = import.meta.env.VITE_NODE_ENV !== 'production';

const config: DatabaseConfig = {
  host: isDevelopment ? import.meta.env.VITE_DB_HOST || 'localhost' : import.meta.env.VITE_PROD_DB_HOST,
  port: parseInt(import.meta.env.VITE_DB_PORT || '3306'),
  user: isDevelopment ? import.meta.env.VITE_DB_USER || 'root' : import.meta.env.VITE_PROD_DB_USER,
  password: isDevelopment ? import.meta.env.VITE_DB_PASSWORD || '' : import.meta.env.VITE_PROD_DB_PASSWORD,
  database: isDevelopment ? import.meta.env.VITE_DB_NAME || 'teamwork_app' : import.meta.env.VITE_PROD_DB_NAME,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
};

// Create connection pool
export const pool = mysql.createPool(config);

// Test connection function
export const testConnection = async (): Promise<boolean> => {
  try {
    const connection = await pool.getConnection();
    await connection.ping();
    connection.release();
    console.log('Database connection successful');
    return true;
  } catch (error) {
    console.error('Database connection failed:', error);
    return false;
  }
};

// Execute query helper function
export const executeQuery = async (query: string, params: any[] = []): Promise<any> => {
  try {
    const [results] = await pool.execute(query, params);
    return results;
  } catch (error) {
    console.error('Query execution failed:', error);
    throw error;
  }
};

// Get single record helper
export const getOne = async (query: string, params: any[] = []): Promise<any> => {
  const results = await executeQuery(query, params);
  return Array.isArray(results) && results.length > 0 ? results[0] : null;
};

// Get multiple records helper
export const getMany = async (query: string, params: any[] = []): Promise<any[]> => {
  const results = await executeQuery(query, params);
  return Array.isArray(results) ? results : [];
};

// Insert record helper
export const insertRecord = async (query: string, params: any[] = []): Promise<number> => {
  const result: any = await executeQuery(query, params);
  return result.insertId;
};

// Update record helper
export const updateRecord = async (query: string, params: any[] = []): Promise<number> => {
  const result: any = await executeQuery(query, params);
  return result.affectedRows;
};

// Delete record helper
export const deleteRecord = async (query: string, params: any[] = []): Promise<number> => {
  const result: any = await executeQuery(query, params);
  return result.affectedRows;
};

export default pool;
