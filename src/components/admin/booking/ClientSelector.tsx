import React, { useState, useEffect } from 'react';
import { Client } from '../../../types';
import { getClients } from '../../../services/firebase';
import { Search, User } from 'lucide-react';

interface ClientSelectorProps {
  selectedClientEmail: string;
  onClientSelect: (client: Client) => void;
}

const ClientSelector: React.FC<ClientSelectorProps> = ({ selectedClientEmail, onClientSelect }) => {
  const [clients, setClients] = useState<Client[]>([]);
  const [filteredClients, setFilteredClients] = useState<Client[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isOpen, setIsOpen] = useState(false);
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);

  // Fetch clients on component mount
  useEffect(() => {
    const fetchClients = async () => {
      try {
        setIsLoading(true);
        const fetchedClients = await getClients();
        setClients(fetchedClients);
        setFilteredClients(fetchedClients);
        
        // Set the initially selected client
        const initialClient = fetchedClients.find(client => client.email.toLowerCase() === selectedClientEmail.toLowerCase());
        if (initialClient) {
          setSelectedClient(initialClient);
        }
        
        setIsLoading(false);
      } catch (error) {
        console.error('Error fetching clients:', error);
        setIsLoading(false);
      }
    };

    fetchClients();
  }, [selectedClientEmail]);

  // Filter clients based on search term
  useEffect(() => {
    if (searchTerm.trim() === '') {
      setFilteredClients(clients);
    } else {
      const filtered = clients.filter(client => 
        client.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        client.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        client.phone.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredClients(filtered);
    }
  }, [searchTerm, clients]);

  // Handle client selection
  const handleClientSelect = (client: Client) => {
    setSelectedClient(client);
    onClientSelect(client);
    setIsOpen(false);
    setSearchTerm('');
  };

  return (
    <div className="relative w-full">
      <div className="mb-2 font-medium">Client</div>
      
      {/* Selected client display or search input */}
      <div 
        className="flex items-center justify-between p-2 border rounded-md cursor-pointer hover:bg-gray-50"
        onClick={() => setIsOpen(!isOpen)}
      >
        {selectedClient ? (
          <div className="flex items-center">
            <User size={18} className="mr-2 text-gray-500" />
            <div>
              <div className="font-medium">{selectedClient.name}</div>
              <div className="text-sm text-gray-500">{selectedClient.email}</div>
            </div>
          </div>
        ) : (
          <div className="text-gray-500">Select a client</div>
        )}
        <div className="text-gray-400">▼</div>
      </div>
      
      {/* Dropdown for client selection */}
      {isOpen && (
        <div className="absolute z-10 w-full mt-1 bg-white border rounded-md shadow-lg max-h-60 overflow-y-auto">
          <div className="p-2 border-b">
            <div className="relative">
              <input
                type="text"
                placeholder="Search clients..."
                className="w-full p-2 pl-8 border rounded-md"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              <Search size={16} className="absolute left-2 top-3 text-gray-400" />
            </div>
          </div>
          
          {isLoading ? (
            <div className="p-4 text-center text-gray-500">Loading clients...</div>
          ) : filteredClients.length === 0 ? (
            <div className="p-4 text-center text-gray-500">No clients found</div>
          ) : (
            <ul>
              {filteredClients.map(client => (
                <li 
                  key={client.id}
                  className={`p-2 cursor-pointer hover:bg-gray-100 ${
                    selectedClient?.id === client.id ? 'bg-blue-50' : ''
                  }`}
                  onClick={() => handleClientSelect(client)}
                >
                  <div className="font-medium">{client.name}</div>
                  <div className="text-sm text-gray-500">{client.email}</div>
                  <div className="text-sm text-gray-500">{client.phone}</div>
                </li>
              ))}
            </ul>
          )}
        </div>
      )}
    </div>
  );
};

export default ClientSelector;
