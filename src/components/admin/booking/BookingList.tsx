import React, { useState } from 'react';
import { FileText, ChevronDown, ChevronUp, Calendar, Download, Plus, Trash2, DollarSign, CreditCard, Edit, Building, Camera, Clock, UserCog } from 'lucide-react';
import { Booking, Payment, Product, SystemSettings, Client } from '../../../types';
import { generatePDF } from '../../../services/pdfGenerator';
import { updateBooking } from '../../../services/firebase';
import { formatCurrency } from '../../../utils';
import BookingItems from './BookingItems';
import * as htmlToImage from 'html-to-image';
import { formatRentalDates } from '../../../utils/dateUtils';

// Import modal components
import DatePickerModal from './modals/DatePickerModal';
import EditBookingItemsModal from './modals/EditBookingItemsModal';
import DiscountModal from './modals/DiscountModal';
import PaymentModal from './modals/PaymentModal';
import ClientEditModal from './modals/ClientEditModal';

interface BookingListProps {
  bookings: Booking[];
  products: Product[];
  systemSettings: SystemSettings;
  onBookingsUpdate: (bookings: Booking[]) => void;
  expandedBookingId: string | null;
  setExpandedBookingId: (id: string | null) => void;
  onDeleteBooking: (id: string) => void;
  onAddPayment: (bookingId: string) => void;
  onEditPayment: (bookingId: string, paymentId: string) => void;
  onDeletePayment: (bookingId: string, paymentId: string) => void;
  formatDate: (date: string) => string;
  getPaymentStatus: (booking: Booking) => string;
  getPaymentStatusBadgeClass: (status: string) => string;
  calculatePaymentProgress: (booking: Booking) => number;
  onStatusChange: (bookingId: string, status: 'pending' | 'confirmed' | 'completed' | 'cancelled') => void;
}



const BookingList: React.FC<BookingListProps> = ({
  bookings,
  products,
  systemSettings,
  onBookingsUpdate,
  expandedBookingId,
  setExpandedBookingId,
  onDeleteBooking,
  onAddPayment,
  onEditPayment,
  onDeletePayment,
  formatDate,
  getPaymentStatus,
  getPaymentStatusBadgeClass,
  calculatePaymentProgress,
  onStatusChange
}) => {
  // Add new state variables for separate editing modes
  const [editingBooking, setEditingBooking] = useState<Booking | null>(null);
  const [editingDiscount, setEditingDiscount] = useState<Booking | null>(null);
  const [editingDates, setEditingDates] = useState<Booking | null>(null);
  const [editingClient, setEditingClient] = useState<Booking | null>(null);

  // Payment related state
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [paymentBookingId, setPaymentBookingId] = useState<string | null>(null);
  const [editingPaymentId, setEditingPaymentId] = useState<string | null>(null);
  const [newPayment, setNewPayment] = useState<{
    amount: string;
    method: 'cash' | 'card' | 'bank_transfer';
    reference: string;
    notes: string;
    date: string;
  }>({
    amount: '',
    method: 'cash',
    reference: '',
    notes: '',
    date: new Date().toISOString().split('T')[0] // Set default to today's date
  });

  // Add the state for the date picker modal
  const [showDatePickerModal, setShowDatePickerModal] = useState(false);

  // Calculate item total based on rental type and custom days
  const calculateItemTotal = (product: Product, booking: Booking) => {
    const productDays = product.customDays || booking.rentalPeriod.days;
    const dailyRate = product.temporaryDailyRate || product.dailyRate;
    const weeklyRate = product.temporaryWeeklyRate || product.weeklyRate;

    if (booking.rentalPeriod.rentalType === 'weekly' && weeklyRate) {
      const weeks = Math.ceil(productDays / 7);
      return weeklyRate * weeks * product.quantity;
    } else {
      return dailyRate * productDays * product.quantity;
    }
  };

  // Calculate total amount for booking
  const calculateBookingTotal = (booking: Booking) => {
    // Calculate subtotal from products
    const subtotal = booking.products.reduce((sum, product) =>
      sum + calculateItemTotal(product, booking), 0);

    // Add delivery fee
    const deliveryFee = booking.delivery?.fee || 0;
    const subtotalWithDelivery = subtotal + deliveryFee;

    // Calculate discount
    const discount = booking.coupon ?
      (booking.coupon.discountType === 'percentage' ?
        (subtotalWithDelivery * booking.coupon.discountValue / 100) :
        booking.coupon.discountValue) : 0;

    // Calculate tax
    const taxableAmount = subtotalWithDelivery - discount;
    const tax = taxableAmount * (systemSettings.enableTax ? systemSettings.taxRate / 100 : 0);

    // Return final total
    return booking.status === 'cancelled' ? 0 : (taxableAmount + tax);
  };

  // Get status badge class
  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'confirmed':
        return 'bg-blue-100 text-blue-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-yellow-100 text-yellow-800';
    }
  };

  // Handle PDF generation
  const handleGeneratePDF = async (booking: Booking, e?: React.MouseEvent) => {
    try {
      // Check if it's a mobile device and detect iOS specifically
      const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);
      const isIOS = /iPhone|iPad|iPod/i.test(navigator.userAgent);

      if (isMobile && e) {
        e.stopPropagation();

        // Show options modal for mobile
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4';

        const modalContent = document.createElement('div');
        modalContent.className = 'bg-white rounded-lg shadow-lg w-full max-w-xs p-4';

        const title = document.createElement('h3');
        title.className = 'text-lg font-bold mb-4 text-center';
        title.textContent = 'Share or Download PDF';

        const buttonsContainer = document.createElement('div');
        buttonsContainer.className = 'flex flex-col space-y-3';

        // iOS Share buttons (only shown on iOS devices)
        let iosShareButton;
        let iosWhatsAppBusinessButton;
        if (isIOS) {
          // General iOS Share button
          iosShareButton = document.createElement('button');
          iosShareButton.className = 'flex items-center justify-center p-3 bg-blue-500 text-white rounded-md';
          iosShareButton.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="white"><path d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .24.04.47.09.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65 0 1.61 1.31 2.92 2.92 2.92 1.61 0 2.92-1.31 2.92-2.92s-1.31-2.92-2.92-2.92z"/></svg><span class="ml-2">iOS Share (Any App)</span>';

          // iOS WhatsApp Business button (only if client has phone number)
          if (booking.customer.phone && booking.customer.phone.trim() !== '') {
            iosWhatsAppBusinessButton = document.createElement('button');
            iosWhatsAppBusinessButton.className = 'flex items-center justify-center p-3 bg-blue-600 text-white rounded-md';
            iosWhatsAppBusinessButton.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="white"><path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893a11.821 11.821 0 00-3.48-8.413z"/></svg><span class="ml-2">iOS Share to Client (WhatsApp)</span>';
          }
        }

        // Regular WhatsApp button
        const whatsappButton = document.createElement('button');
        whatsappButton.className = 'flex items-center justify-center p-3 bg-green-500 text-white rounded-md';
        whatsappButton.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="white"><path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893a11.821 11.821 0 00-3.48-8.413z"/></svg><span class="ml-2">WhatsApp + Download PDF</span>';

        // WhatsApp Business button
        const whatsappBusinessButton = document.createElement('button');
        whatsappBusinessButton.className = 'flex items-center justify-center p-3 bg-green-600 text-white rounded-md';
        whatsappBusinessButton.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="white"><path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893a11.821 11.821 0 00-3.48-8.413z"/></svg><span class="ml-2">WhatsApp Business + PDF (Client)</span>';

        // Download button
        const downloadButton = document.createElement('button');
        downloadButton.className = 'flex items-center justify-center p-3 bg-blue-600 text-white rounded-md';
        downloadButton.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path><polyline points="7 10 12 15 17 10"></polyline><line x1="12" y1="15" x2="12" y2="3"></line></svg><span class="ml-2">Download PDF</span>';

        // Cancel button
        const cancelButton = document.createElement('button');
        cancelButton.className = 'flex items-center justify-center p-3 bg-gray-200 text-gray-800 rounded-md';
        cancelButton.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg><span class="ml-2">Cancel</span>';

        // Add event listeners
        if (isIOS) {
          // Event listener for general iOS Share button
          if (iosShareButton) {
            iosShareButton.addEventListener('click', async () => {
            try {
              // Generate PDF as data URL
              const pdfDataUrl = await generatePDF({
                ...booking,
                settings: systemSettings
              }, false) as string;

              // Create a blob from the data URL
              const dataUrlParts = pdfDataUrl.split(',');
              const mimeMatch = dataUrlParts[0].match(/:(.*?);/);
              const mimeType = mimeMatch && mimeMatch[1] ? mimeMatch[1] : 'application/pdf';
              const decodedData = atob(dataUrlParts[1]);
              const uInt8Array = new Uint8Array(decodedData.length);

              for (let i = 0; i < decodedData.length; ++i) {
                uInt8Array[i] = decodedData.charCodeAt(i);
              }

              const blob = new Blob([uInt8Array], { type: mimeType });

              // Create a File object
              const file = new File([blob], `TW_${booking.quoteNumber}.pdf`, { type: mimeType });

              // Use the Web Share API
              if (navigator.share) {
                await navigator.share({
                  title: `Quote #${booking.quoteNumber} for ${booking.customer.name}`,
                  text: `Quote #${booking.quoteNumber} for ${booking.customer.name}\nTotal: BHD ${booking.total.toFixed(3)}`,
                  files: [file]
                });
              } else {
                // Fallback for browsers that don't support file sharing
                const link = document.createElement('a');
                link.href = pdfDataUrl;
                link.download = `TW_${booking.quoteNumber}.pdf`;
                link.click();

                alert('Your browser does not support the Share API. The PDF has been downloaded instead.');
              }

              // Remove the modal
              document.body.removeChild(modal);
            } catch (error) {
              console.error('Error using Share API:', error);

              // Fallback to download if sharing fails
              try {
                await generatePDF({
                  ...booking,
                  settings: systemSettings
                });

                // Remove the modal
                document.body.removeChild(modal);
              } catch (downloadError) {
                console.error('Error downloading PDF:', downloadError);
              }
            }
          });
          }

          // Event listener for iOS WhatsApp Business button
          if (iosWhatsAppBusinessButton) {
            iosWhatsAppBusinessButton.addEventListener('click', async () => {
              try {
                // Generate PDF as data URL
                const pdfDataUrl = await generatePDF({
                  ...booking,
                  settings: systemSettings
                }, false) as string;

                // Format the phone number for WhatsApp
                let phoneNumber = booking.customer.phone.replace(/[\s-\(\)]/g, '');

                // Add country code if not present
                if (!phoneNumber.startsWith('+')) {
                  // Assume Bahrain (+973) if no country code
                  if (!phoneNumber.startsWith('973')) {
                    phoneNumber = '973' + phoneNumber;
                  }
                  phoneNumber = '+' + phoneNumber;
                }

                // Remove the + for the wa.me format
                phoneNumber = phoneNumber.replace('+', '');

                // Create the message
                const message = `Quote #${booking.quoteNumber} for ${booking.customer.name}\nTotal: BHD ${booking.total.toFixed(3)}\n\nThe PDF has been downloaded to your device. Please send it as a separate attachment after this message.`;

                // Create a blob and download the PDF
                const link = document.createElement('a');
                link.href = pdfDataUrl;
                link.download = `TW_${booking.quoteNumber}.pdf`;
                link.click();

                // Open WhatsApp with the client's number
                window.open(`https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`, '_blank');

                // Remove the modal
                document.body.removeChild(modal);
              } catch (error) {
                console.error('Error sharing via WhatsApp Business:', error);

                // Fallback to download if sharing fails
                try {
                  await generatePDF({
                    ...booking,
                    settings: systemSettings
                  });

                  // Remove the modal
                  document.body.removeChild(modal);
                } catch (downloadError) {
                  console.error('Error downloading PDF:', downloadError);
                }
              }
            });
          }
        }

        whatsappButton.addEventListener('click', async () => {
          try {
            // Generate PDF as data URL
            const pdfDataUrl = await generatePDF({
              ...booking,
              settings: systemSettings
            }, false) as string;

            // Create a regular WhatsApp share link with the booking details
            const message = `Quote #${booking.quoteNumber} for ${booking.customer.name}\nTotal: BHD ${booking.total.toFixed(3)}\n\nThe PDF has been downloaded to your device. Please send it as a separate attachment after this message.`;

            // For regular WhatsApp, always open without a specific recipient
            // This allows the user to select any contact to share with
            const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`;

            // Open WhatsApp in a new tab
            window.open(whatsappUrl, '_blank');

            // Also download the PDF
            const link = document.createElement('a');
            link.href = pdfDataUrl;
            link.download = `TW_${booking.quoteNumber}.pdf`;
            link.click();

            // Remove the modal
            document.body.removeChild(modal);
          } catch (error) {
            console.error('Error sharing via WhatsApp:', error);
          }
        });

        whatsappBusinessButton.addEventListener('click', async () => {
          try {
            // Generate PDF as data URL
            const pdfDataUrl = await generatePDF({
              ...booking,
              settings: systemSettings
            }, false) as string;

            // Create a WhatsApp Business share link with the booking details
            const message = `Quote #${booking.quoteNumber} for ${booking.customer.name}\nTotal: BHD ${booking.total.toFixed(3)}\n\nThe PDF has been downloaded to your device. Please send it as a separate attachment after this message.`;

            // Use client's phone number if available, otherwise open without a specific recipient
            let whatsappUrl = '';
            if (booking.customer.phone && booking.customer.phone.trim() !== '') {
              // Format the phone number (remove spaces, dashes, etc.)
              let phoneNumber = booking.customer.phone.replace(/[\s-\(\)]/g, '');

              // Add country code if not present
              if (!phoneNumber.startsWith('+')) {
                // Assume Bahrain (+973) if no country code
                if (!phoneNumber.startsWith('973')) {
                  phoneNumber = '973' + phoneNumber;
                }
                phoneNumber = '+' + phoneNumber;
              }

              whatsappUrl = `https://api.whatsapp.com/send?phone=${phoneNumber}&text=${encodeURIComponent(message)}`;
            } else {
              whatsappUrl = `https://api.whatsapp.com/send?text=${encodeURIComponent(message)}`;
            }

            // Open WhatsApp Business in a new tab
            window.open(whatsappUrl, '_blank');

            // Also download the PDF
            const link = document.createElement('a');
            link.href = pdfDataUrl;
            link.download = `TW_${booking.quoteNumber}.pdf`;
            link.click();

            // Remove the modal
            document.body.removeChild(modal);
          } catch (error) {
            console.error('Error sharing via WhatsApp Business:', error);
          }
        });

        downloadButton.addEventListener('click', async () => {
          try {
            // Generate and download PDF
            await generatePDF({
              ...booking,
              settings: systemSettings
            });

            // Remove the modal
            document.body.removeChild(modal);
          } catch (error) {
            console.error('Error downloading PDF:', error);
          }
        });

        cancelButton.addEventListener('click', () => {
          document.body.removeChild(modal);
        });

        // Assemble and show the modal
        if (isIOS && iosShareButton) {
          buttonsContainer.appendChild(iosShareButton);
        }
        buttonsContainer.appendChild(whatsappButton);
        buttonsContainer.appendChild(whatsappBusinessButton);
        buttonsContainer.appendChild(downloadButton);
        buttonsContainer.appendChild(cancelButton);

        modalContent.appendChild(title);
        modalContent.appendChild(buttonsContainer);

        modal.appendChild(modalContent);
        document.body.appendChild(modal);
      } else {
        // Desktop: just generate and download the PDF
        await generatePDF({
          ...booking,
          settings: systemSettings
        });
      }
    } catch (error) {
      console.error('Error generating PDF:', error);
    }
  };

  // Handle editing booking items
  const handleEditItems = (booking: Booking) => {
    setEditingBooking({ ...booking });
  };

  // Handle changing client
  const handleChangeClient = (booking: Booking) => {
    setEditingClient({ ...booking });
  };

  // Handle client selection
  const handleClientSelect = (client: Client) => {
    if (!editingClient) return;

    // Update the booking with the new client information
    const updatedBooking = {
      ...editingClient,
      customer: {
        name: client.name,
        email: client.email,
        phone: client.phone,
        address: client.address
      }
    };

    setEditingClient(updatedBooking);
  };

  // Save client changes
  const handleSaveClientChanges = async () => {
    if (!editingClient) return;

    try {
      // Update the booking in the database
      await updateBooking(editingClient);

      // Update the local state
      const updatedBookings = bookings.map(b =>
        b.id === editingClient.id ? editingClient : b
      );

      onBookingsUpdate(updatedBookings);
      setEditingClient(null);
    } catch (error) {
      console.error('Error updating client:', error);
      alert(`Failed to update client: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  // Save booking changes
  const handleSaveBookingChanges = () => {
    if (editingBooking) {
      // Update the booking in the database
      updateBooking(editingBooking).then(() => {
        // Update the local state
        const updatedBookings = bookings.map(b =>
          b.id === editingBooking.id ? editingBooking : b
        );
        onBookingsUpdate(updatedBookings);
        setEditingBooking(null);
      }).catch((error: Error) => {
        console.error('Error updating booking:', error);
        // Show error message to user
        alert(`Failed to update booking: ${error.message}`);
        // Revert the local state if the update fails
        setEditingBooking(null);
      });
    }
  };

  // Handle adding a payment
  const handleLocalAddPayment = (bookingId: string) => {
    setPaymentBookingId(bookingId);
    setNewPayment({
      amount: '',
      method: 'bank_transfer', // Set default to bank_transfer
      reference: '',
      notes: '',
      date: new Date().toISOString().split('T')[0] // Set default to today's date
    });
    setEditingPaymentId(null);
    setShowPaymentModal(true);
  };

  // Handle editing a payment
  const handleLocalEditPayment = (bookingId: string, paymentId: string) => {
    const booking = bookings.find(b => b.id === bookingId);
    if (!booking || !booking.payments) return;

    const payment = booking.payments.find(p => p.id === paymentId);
    if (!payment) return;

    // Populate the payment form with existing data
    setPaymentBookingId(bookingId);
    setNewPayment({
      amount: payment.amount.toString(),
      method: payment.method as 'cash' | 'card' | 'bank_transfer',
      reference: payment.reference || '',
      notes: payment.notes || '',
      date: payment.date || new Date().toISOString().split('T')[0] // Use payment date or today's date
    });

    // Set editing state with payment ID
    setEditingPaymentId(paymentId);
    setShowPaymentModal(true);
  };

  // Handle submitting payment
  const handleSubmitPayment = () => {
    if (!paymentBookingId) return;

    const booking = bookings.find(b => b.id === paymentBookingId);
    if (!booking) return;

    // Validate amount
    const amount = parseFloat(newPayment.amount);
    if (isNaN(amount) || amount <= 0) {
      alert('Please enter a valid payment amount');
      return;
    }

    let updatedBooking;

    if (editingPaymentId) {
      // Editing existing payment
      updatedBooking = {
        ...booking,
        payments: (booking.payments || []).map(payment => {
          if (payment.id === editingPaymentId) {
            // Create a new payment object without undefined values
            const updatedPayment = {
              ...payment,
              amount: amount,
              method: newPayment.method
            };

            // Only add non-empty properties
            if (newPayment.reference && newPayment.reference.trim() !== '') {
              updatedPayment.reference = newPayment.reference;
            }
            if (newPayment.notes && newPayment.notes.trim() !== '') {
              updatedPayment.notes = newPayment.notes;
            }
            if (newPayment.date) {
              updatedPayment.date = new Date(newPayment.date).toISOString();
            }

            return updatedPayment;
          }
          return payment;
        })
      };
    } else {
      // Adding new payment
      const paymentData: Payment = {
        id: Date.now().toString(),
        bookingId: paymentBookingId,
        amount: amount,
        transactionId: `TXN-${Date.now()}`,
        date: newPayment.date ? new Date(newPayment.date).toISOString() : new Date().toISOString(),
        method: newPayment.method,
        status: 'completed'
      };

      // Only add non-empty properties
      if (newPayment.reference && newPayment.reference.trim() !== '') {
        paymentData.reference = newPayment.reference;
      }
      if (newPayment.notes && newPayment.notes.trim() !== '') {
        paymentData.notes = newPayment.notes;
      }

      updatedBooking = {
        ...booking,
        payments: [...(booking.payments || []), paymentData]
      };
    }

    // Update the booking in the database
    updateBooking(updatedBooking)
      .then(() => {
        // Update the local state
        const updatedBookings = bookings.map(b =>
          b.id === paymentBookingId ? updatedBooking : b
        );
        onBookingsUpdate(updatedBookings);
        setShowPaymentModal(false);
        setPaymentBookingId(null);
        setEditingPaymentId(null);
      })
      .catch((error) => {
        console.error('Error updating payment:', error);
        alert(`Failed to update payment: ${error.message}`);
      });
  };

  // Handle generating a snapshot image of the booking card
  const handleGenerateSnapshot = async (booking: Booking) => {
    try {
      // Create a separate snapshot container
      const snapshotContainer = document.createElement('div');
      snapshotContainer.className = 'bg-white p-6 rounded-lg shadow-lg';
      snapshotContainer.style.width = '350px';
      snapshotContainer.style.position = 'fixed';
      snapshotContainer.style.top = '0';
      snapshotContainer.style.left = '0';
      snapshotContainer.style.zIndex = '-1000';
      document.body.appendChild(snapshotContainer);

      // Add header with company branding
      const header = document.createElement('div');
      header.className = 'mb-4 pb-3 border-b border-gray-200';
      header.innerHTML = `
        <div class="flex items-center justify-between">
          <div class="logo-placeholder text-blue-600">
            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="24" viewBox="0 0 282 139" preserveAspectRatio="xMidYMid meet">
              <g transform="translate(0.000000,139.000000) scale(0.100000,-0.100000)" fill="currentColor" stroke="none">
                <path d="M140 1210 l0 -179 -67 -3 -68 -3 -3 -127 -3 -128 71 0 70 0 0 -192 c0 -234 11 -313 51 -395 55 -113 139 -158 294 -158 90 1 182 21 219 48 18 13 17 18 -19 138 -21 68 -39 127 -41 130 -2 4 -22 1 -44 -7 -52 -18 -86 -5 -105 41 -11 26 -15 80 -15 215 l0 180 83 0 82 0 191 -358 192 -357 114 -3 115 -3 56 123 c30 68 79 *********** 29 66 55 123 58 127 3 4 56 -104 116 -240 l111 -247 114 -3 115 -3 258 480 c141 *********** 257 491 0 6 -66 10 -189 10 l-188 0 -120 -265 -120 -266 -118 266 -119 266 -114 -3 -115 -3 -117 -263 -117 -263 -119 263 -120 263 -207 3 -207 2 0 180 0 180 -170 0 -170 0 0 -180z"/>
                <path d="M2521 450 c-92 -23 -161 -111 -161 -208 0 -217 270 -301 395 -124 27 40 30 51 30 121 0 70 -3 82 -29 120 -51 74 -150 112 -235 91z"/>
              </g>
            </svg>
          </div>
          <div class="text-right">
            <p class="text-sm font-medium">Booking Snapshot</p>
            <p class="text-xs text-gray-500">Generated on ${new Date().toLocaleDateString()}</p>
          </div>
        </div>
      `;
      snapshotContainer.appendChild(header);

      // Quote number and date section
      const quoteSection = document.createElement('div');
      quoteSection.className = 'mb-4';
      quoteSection.innerHTML = `
        <div class="grid grid-cols-2 gap-4">
          <div>
            <p class="text-sm text-gray-500">Quote #:</p>
            <p class="text-base font-medium">${booking.quoteNumber}</p>
          </div>
          <div class="text-right">
            <p class="text-sm text-gray-500">Booking Date:</p>
            <p class="text-sm font-medium">${formatDate(booking.date)}</p>
          </div>
        </div>
      `;
      snapshotContainer.appendChild(quoteSection);

      // Customer information
      const customerSection = document.createElement('div');
      customerSection.className = 'mb-4 p-3 bg-gray-50 rounded';
      customerSection.innerHTML = `
        <h3 class="text-sm font-semibold mb-2 text-center">Customer Information</h3>
        <p class="text-sm"><span class="font-medium">Name:</span> ${booking.customer.name}</p>
        <p class="text-sm"><span class="font-medium">Email:</span> ${booking.customer.email}</p>
        <p class="text-sm"><span class="font-medium">Phone:</span> ${booking.customer.phone || 'N/A'}</p>
      `;
      snapshotContainer.appendChild(customerSection);

      // Rental period
      const rentalSection = document.createElement('div');
      rentalSection.className = 'mb-4 p-3 bg-blue-50 rounded';
      rentalSection.innerHTML = `
        <h3 class="text-sm font-semibold mb-2">Booking Duration: <span class="px-2 py-1 text-xs font-medium bg-indigo-100 text-indigo-800 rounded-full">${booking.rentalPeriod.days} ${booking.rentalPeriod.days === 1 ? 'day' : 'days'}</span></h3>
        <div class="grid grid-cols-1 gap-2 mb-3">
          <div>
            <p class="text-xs text-gray-500">Selected Dates</p>
            <p class="text-sm font-medium">${booking.rentalPeriod.dates ? formatRentalDates(booking.rentalPeriod.dates) : 'No dates selected'}</p>
          </div>
        </div>
        <div class="mt-3 text-center">
          <span class="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800">
            Total Booked Items: ${booking.products.reduce((sum, product) => sum + product.quantity, 0)}
          </span>
        </div>
      `;
      snapshotContainer.appendChild(rentalSection);

      // Cost summary
      const costSummary = document.createElement('div');
      costSummary.className = 'mt-4 pt-3 border-t border-gray-200';

      const calculatedTotal = calculateBookingTotal(booking);
      const paidAmount = (booking.payments || []).reduce((sum, payment) => sum + payment.amount, 0);
      const remainingAmount = calculatedTotal - paidAmount;
      const paymentStatus = getPaymentStatus(booking);

      costSummary.innerHTML = `
        <div class="flex justify-between mb-1">
          <p class="text-sm">Subtotal:</p>
          <p class="text-sm font-medium">${formatCurrency(booking.subtotal)}</p>
        </div>
        ${booking.deliveryFee > 0 ? `
        <div class="flex justify-between mb-1">
          <p class="text-sm">Delivery:</p>
          <p class="text-sm">${formatCurrency(booking.deliveryFee)}</p>
        </div>
        ` : ''}
        ${booking.discount > 0 ? `
        <div class="flex justify-between mb-1">
          <p class="text-sm">Discount:</p>
          <p class="text-sm text-green-600">-${formatCurrency(booking.discount)}</p>
        </div>
        ` : ''}
        <div class="flex justify-between mb-1">
          <p class="text-sm">VAT (${systemSettings.taxRate || 0}%):</p>
          <p class="text-sm">${formatCurrency(booking.tax || 0)}</p>
        </div>
        <div class="flex justify-between mt-2 pt-2 border-t border-gray-200">
          <p class="text-sm font-bold">Total:</p>
          <p class="text-sm font-bold">${formatCurrency(calculatedTotal)}</p>
        </div>
        ${paidAmount > 0 ? `
        <div class="flex justify-between mt-1">
          <p class="text-sm">Paid:</p>
          <p class="text-sm text-green-600">${formatCurrency(paidAmount)}</p>
        </div>
        ` : ''}
        ${remainingAmount > 0 ? `
        <div class="flex justify-between mt-1">
          <p class="text-sm">Balance:</p>
          <p class="text-sm text-red-600">${formatCurrency(remainingAmount)}</p>
        </div>
        ` : ''}
        <div class="mt-3 flex justify-center">
          <span class="px-3 py-1 text-xs font-semibold rounded-full ${getPaymentStatusBadgeClass(paymentStatus)} capitalize">
            ${paymentStatus === 'paid' ? 'Fully Paid' : paymentStatus === 'partial' ? 'Partially Paid' : 'Unpaid'}
          </span>
        </div>
      `;

      snapshotContainer.appendChild(costSummary);

      // Add footer
      const footer = document.createElement('div');
      footer.className = 'mt-4 pt-3 border-t border-gray-200 text-center';
      footer.innerHTML = `
        <p class="text-xs text-gray-500">Thank you for your business!</p>
        <p class="text-xs text-gray-500"><EMAIL>   |  +973 39 963 468</p>
        <p class="text-xs text-gray-500">Team Work Publicity Co. W.L.L   |  CR: 18302201</p>
        <p class="text-xs text-gray-400">Kingdom of Bahrain</p>
      `;
      snapshotContainer.appendChild(footer);

      // Add Tailwind styles directly to ensure proper rendering
      const tailwindStyles = document.createElement('style');
      tailwindStyles.textContent = `
        .bg-white { background-color: white; }
        .p-6 { padding: 1.5rem; }
        .rounded-lg { border-radius: 0.5rem; }
        .shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }
        .mb-4 { margin-bottom: 1rem; }
        .pb-3 { padding-bottom: 0.75rem; }
        .border-b { border-bottom-width: 1px; }
        .border-gray-200 { border-color: #e5e7eb; }
        .flex { display: flex; }
        .items-center { align-items: center; }
        .justify-between { justify-content: space-between; }
        .text-xl { font-size: 1.25rem; line-height: 1.75rem; }
        .font-bold { font-weight: 700; }
        .text-blue-600 { color: #2563eb; }
        .text-right { text-align: right; }
        .text-sm { font-size: 0.875rem; line-height: 1.25rem; }
        .font-medium { font-weight: 500; }
        .text-xs { font-size: 0.75rem; line-height: 1rem; }
        .text-gray-500 { color: #6b7280; }
        .ml-1 { margin-left: 0.25rem; }
        .p-3 { padding: 0.75rem; }
        .bg-gray-50 { background-color: #f9fafb; }
        .rounded { border-radius: 0.25rem; }
        .font-semibold { font-weight: 600; }
        .mb-2 { margin-bottom: 0.5rem; }
        .bg-blue-50 { background-color: #eff6ff; }
        .grid { display: grid; }
        .grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
        .gap-2 { gap: 0.5rem; }
        .mt-2 { margin-top: 0.5rem; }
        .px-2 { padding-left: 0.5rem; padding-right: 0.5rem; }
        .py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }
        .bg-indigo-100 { background-color: #e0e7ff; }
        .text-indigo-800 { color: #3730a3; }
        .flex-wrap { flex-wrap: wrap; }
        .gap-2 { gap: 0.5rem; }
        .mb-3 { margin-bottom: 0.75rem; }
        .rounded-full { border-radius: 9999px; }
        .bg-blue-100 { background-color: #dbeafe; }
        .text-blue-800 { color: #1e40af; }
        .bg-gray-100 { background-color: #f3f4f6; }
        .text-gray-800 { color: #1f2937; }
        .space-y-2 > * + * { margin-top: 0.5rem; }
        .p-2 { padding: 0.5rem; }
        .border { border-width: 1px; }
        .border-gray-200 { border-color: #e5e7eb; }
        .mr-1 { margin-right: 0.25rem; }
        .mx-1 { margin-left: 0.25rem; margin-right: 0.25rem; }
        .mt-4 { margin-top: 1rem; }
        .pt-3 { padding-top: 0.75rem; }
        .mb-1 { margin-bottom: 0.25rem; }
        .text-green-600 { color: #059669; }
        .mt-3 { margin-top: 0.75rem; }
        .justify-center { justify-content: center; }
        .px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
        .text-center { text-align: center; }
        .text-red-600 { color: #dc2626; }
      `;
      snapshotContainer.appendChild(tailwindStyles);

      // Wait for styles to be applied
      await new Promise(resolve => setTimeout(resolve, 100));

      // Generate the image
      const dataUrl = await htmlToImage.toPng(snapshotContainer, {
        quality: 1.0,
        pixelRatio: 2,
        width: snapshotContainer.offsetWidth,
        height: snapshotContainer.offsetHeight,
        canvasWidth: snapshotContainer.offsetWidth * 2,
        canvasHeight: snapshotContainer.offsetHeight * 2,
        backgroundColor: '#ffffff'
      });

      // Remove the temporary container
      document.body.removeChild(snapshotContainer);

      // Detect if on mobile
      const isMobile = window.innerWidth <= 768;

      if (isMobile) {
        // Create a visible link for mobile devices (needed for iOS Safari)
        const tempContainer = document.createElement('div');
        tempContainer.style.position = 'fixed';
        tempContainer.style.top = '0';
        tempContainer.style.left = '0';
        tempContainer.style.right = '0';
        tempContainer.style.backgroundColor = 'rgba(0,0,0,0.8)';
        tempContainer.style.zIndex = '9999';
        tempContainer.style.padding = '20px';
        tempContainer.style.textAlign = 'center';

        const closeButton = document.createElement('button');
        closeButton.innerText = 'Close';
        closeButton.style.backgroundColor = '#ffffff';
        closeButton.style.border = 'none';
        closeButton.style.padding = '8px 16px';
        closeButton.style.borderRadius = '4px';
        closeButton.style.marginBottom = '10px';
        closeButton.onclick = () => document.body.removeChild(tempContainer);

        const link = document.createElement('a');
        link.href = dataUrl;
        link.target = '_blank';
        link.innerText = 'Open Image in New Tab';
        link.style.display = 'block';
        link.style.backgroundColor = '#4F46E5';
        link.style.color = 'white';
        link.style.padding = '12px 20px';
        link.style.borderRadius = '4px';
        link.style.textDecoration = 'none';
        link.style.margin = '10px auto';
        link.style.width = 'fit-content';

        // Add Share button for iOS and supported browsers
        const shareButton = document.createElement('button');
        shareButton.innerText = 'Share via WhatsApp/Message';
        shareButton.style.display = 'block';
        shareButton.style.backgroundColor = '#25D366'; // WhatsApp green
        shareButton.style.color = 'white';
        shareButton.style.padding = '12px 20px';
        shareButton.style.borderRadius = '4px';
        shareButton.style.border = 'none';
        shareButton.style.margin = '10px auto';
        shareButton.style.width = 'fit-content';
        shareButton.style.fontFamily = 'inherit';
        shareButton.style.fontSize = 'inherit';
        shareButton.style.cursor = 'pointer';

        // Web Share API - works on iOS Safari
        shareButton.onclick = async () => {
          try {
            if (navigator.share) {
              // Convert data URL to Blob
              const response = await fetch(dataUrl);
              const blob = await response.blob();

              // Create file from blob
              const file = new File([blob], `Booking_${booking.quoteNumber}.png`, { type: 'image/png' });

              await navigator.share({
                title: `Booking ${booking.quoteNumber}`,
                text: `Quote #${booking.quoteNumber} - Booking Snapshot`,
                files: [file]
              });
            } else {
              // Fallback for browsers that don't support the Web Share API
              alert('Share functionality not supported by your browser. Please use the "Open Image" button instead.');
            }
          } catch (error) {
            console.error('Error sharing:', error);
          }
        };

        tempContainer.appendChild(closeButton);
        tempContainer.appendChild(shareButton);
        tempContainer.appendChild(link);
        document.body.appendChild(tempContainer);

        // Auto-remove after 10 seconds
        setTimeout(() => {
          if (document.body.contains(tempContainer)) {
            document.body.removeChild(tempContainer);
          }
        }, 10000);
      } else {
        // Create a link to download the image on desktop
        const link = document.createElement('a');
        link.download = `Booking_${booking.quoteNumber}.png`;
        link.href = dataUrl;
        link.click();
      }
    } catch (error) {
      console.error('Error generating snapshot:', error);
      alert('Failed to generate snapshot. Please try again.');
    }
  };

  // Render a booking row for desktop view
  const renderDesktopBookingRow = (booking: Booking) => {
    return (
      <tr
        key={booking.id}
        className={`hover:bg-gray-50 cursor-pointer ${expandedBookingId === booking.id ? 'bg-blue-50' : ''}`}
        onClick={() => setExpandedBookingId(expandedBookingId === booking.id ? null : booking.id)}
      >
        <td className="px-6 py-4">
          <div className="flex items-center">
            <div className="min-w-0 flex-1">
              <p className="text-sm font-medium text-blue-600 truncate">
                {booking.quoteNumber}
              </p>
              <p className="text-sm text-gray-500 truncate">
                {booking.customer.name}
              </p>
            </div>
          </div>
        </td>
        <td className="px-6 py-4">
          <div className="flex items-center">
            <Calendar size={16} className="mr-2 text-gray-500" />
            {formatRentalDates(booking.rentalPeriod.dates)}
          </div>
          <div className="flex items-center mt-1">
            <Clock size={16} className="mr-2 text-gray-500" />
            <span className="text-gray-500 text-sm">
              {booking.rentalPeriod.days} {booking.rentalPeriod.days === 1 ? 'day' : 'days'}
            </span>
          </div>
        </td>
        <td className="px-6 py-4 text-center">
          <div>
            <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getPaymentStatusBadgeClass(getPaymentStatus(booking))}`}>
              {getPaymentStatus(booking)}
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-1.5 mt-2">
            <div
              className="bg-blue-600 h-1.5 rounded-full"
              style={{ width: `${calculatePaymentProgress(booking)}%` }}
            ></div>
          </div>
        </td>
        <td className="px-6 py-4 text-right">
          <div className="font-medium">{formatCurrency(booking.total)}</div>
          {booking.paidAmount && booking.paidAmount > 0 && (
            <div className="text-sm text-gray-500">
              Paid: {formatCurrency(booking.paidAmount)}
            </div>
          )}
        </td>
        <td className="px-6 py-4 text-center">
          <div className="flex justify-center items-center space-x-2">
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleGeneratePDF(booking, e);
              }}
              className="p-1 rounded-full hover:bg-gray-100 text-gray-600"
              title="Generate PDF"
            >
              <FileText size={16} />
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleGenerateSnapshot(booking);
              }}
              className="p-1 rounded-full hover:bg-gray-100 text-purple-600"
              title="Download Snapshot"
            >
              <Camera size={16} />
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation();
                // Create a copy and set editingDates
                const editBooking = {...booking};
                setEditingDates(editBooking);
              }}
              className="p-1 rounded-full hover:bg-gray-100 text-blue-600"
              title="Edit Dates"
            >
              <Calendar size={16} />
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleEditItems(booking);
              }}
              className="p-1 rounded-full hover:bg-gray-100 text-indigo-600"
              title="Edit Items"
            >
              <Edit size={16} />
            </button>
          </div>
        </td>
      </tr>
    );
  };

  // Render a mobile booking card for mobile view
  const renderMobileBookingCard = (booking: Booking) => {
    const paymentStatus = getPaymentStatus(booking);
    const calculatedTotal = calculateBookingTotal(booking);
    const paidAmount = (booking.payments || []).reduce((sum, payment) => sum + payment.amount, 0);
    const remainingAmount = calculatedTotal - paidAmount;
    const progressPercentage = calculatePaymentProgress(booking);

    // Calculate total number of items booked
    const totalItems = booking.products.reduce((sum, product) => sum + product.quantity, 0);

    // Group items by category and count them
    const categoryCountMap: {[category: string]: number} = {};
    booking.products.forEach(product => {
      const category = product.category || 'Uncategorized';
      categoryCountMap[category] = (categoryCountMap[category] || 0) + product.quantity;
    });

    // Calculate first and last dates from the dates array for display

    return (
      <div key={booking.id} className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="p-4">
          {/* Header with quote number and actions */}
          <div className="flex justify-between items-start mb-3">
            <div className="flex items-center">
              <div className="h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 mr-3">
                <FileText size={20} />
              </div>
              <div>
                <h3 className="font-medium text-gray-900">{booking.quoteNumber}</h3>
                <p className="text-sm text-gray-500">{formatDate(booking.date)}</p>
              </div>
            </div>

            <div className="flex space-x-2 booking-actions">
              <button
                onClick={() => handleGenerateSnapshot(booking)}
                className="p-2 rounded-full bg-purple-100 text-purple-600"
                title="Generate Snapshot"
              >
                <Camera size={18} />
              </button>
              <button
                onClick={() => handleLocalAddPayment(booking.id)}
                className="p-2 rounded-full bg-green-100 text-green-600"
                title="Add Payment"
              >
                <DollarSign size={18} />
              </button>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handleGeneratePDF(booking, e);
                }}
                className="p-2 rounded-full bg-gray-100 text-gray-600"
                title="Download PDF"
              >
                <Download size={18} />
              </button>
              <button
                onClick={() => setExpandedBookingId(expandedBookingId === booking.id ? null : booking.id)}
                className="p-2 rounded-full bg-gray-100 text-gray-600"
                title={expandedBookingId === booking.id ? "Collapse" : "Expand"}
              >
                {expandedBookingId === booking.id ? (
                  <ChevronUp size={18} />
                ) : (
                  <ChevronDown size={18} />
                )}
              </button>
            </div>
          </div>

          {/* Status and customer info */}
          <div className="flex flex-wrap items-center gap-2 mb-3">
            <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
              getStatusBadgeClass(booking.status)
            } capitalize`}>
              {booking.status}
            </span>
            <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
              getPaymentStatusBadgeClass(paymentStatus)
            } capitalize`}>
              {paymentStatus}
            </span>
            <span className="px-2 py-1 text-xs font-semibold bg-indigo-100 text-indigo-800">
              {booking.rentalPeriod.days} {booking.rentalPeriod.days === 1 ? 'day' : 'days'} Rental
            </span>
          </div>

          <div className="mb-3">
            <p className="text-sm font-medium text-gray-700">
              {booking.customer.name}
            </p>
            <p className="text-xs text-gray-500">{booking.customer.email}</p>
          </div>

          {/* Item count and categories */}
          <div className="flex flex-wrap gap-2 mb-3">
            <span className="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
              Total Items: {totalItems}
            </span>
            {Object.entries(categoryCountMap).map(([category, count]) => (
              <span
                key={category}
                className="px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800"
              >
                {category}: {count}
              </span>
            ))}
          </div>

          {/* Rental period */}
          <div className="grid grid-cols-1 gap-2 mb-3">
            <div>
              <p className="text-xs text-gray-500">Selected Dates</p>
              <p className="text-sm">{formatRentalDates(booking.rentalPeriod.dates)}</p>
            </div>
            <div>
              <p className="text-xs text-gray-500">Duration</p>
              <p className="text-sm">{booking.rentalPeriod.days} {booking.rentalPeriod.days === 1 ? 'day' : 'days'}</p>
            </div>
          </div>

          {/* Amount */}
          <div className="border-t border-gray-200 pt-3">
            <div className="flex justify-between mb-1">
              <p className="text-sm font-medium">Total:</p>
              <p className="text-sm font-medium">{formatCurrency(calculatedTotal)}</p>
            </div>
            {paymentStatus !== 'unpaid' && (
              <div className="flex justify-between mb-1">
                <p className="text-sm text-gray-600">Paid:</p>
                <p className="text-sm text-green-600">{formatCurrency(paidAmount)}</p>
              </div>
            )}
            {paymentStatus === 'partial' && (
              <div className="flex justify-between">
                <p className="text-sm text-gray-600">Remaining:</p>
                <p className="text-sm text-red-600">{formatCurrency(remainingAmount)}</p>
              </div>
            )}
            {paymentStatus !== 'unpaid' && (
              <div className="w-full bg-gray-200 rounded-full h-2.5 mt-2">
                <div
                  className={`h-2.5 rounded-full ${
                    progressPercentage === 100 ? 'bg-green-600' : 'bg-yellow-400'
                  }`}
                  style={{ width: `${progressPercentage}%` }}
                ></div>
              </div>
            )}
          </div>
        </div>

        {/* Include expanded content directly in the card for mobile */}
        {expandedBookingId === booking.id && (
          <div className="bg-gray-50 border-t border-gray-200 p-4">
            {/* Mobile expanded view */}
            <div className="border-t border-gray-200 pt-4 mt-2">
              {/* Status Change */}
              <div className="mb-4">
                <h3 className="font-medium mb-2">Booking Status</h3>
                <div className="flex flex-wrap gap-2">
                  {['pending', 'confirmed', 'completed', 'cancelled'].map(status => (
                    <button
                      key={status}
                      onClick={() => onStatusChange(booking.id, status as any)}
                      className={`px-3 py-1 rounded-md text-xs font-medium ${
                        booking.status === status
                          ? 'bg-blue-600 text-white'
                          : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                      } capitalize`}
                    >
                      {status}
                    </button>
                  ))}
                </div>
              </div>

              {/* Cost Breakdown Section */}
              <div className="mb-4">
                <h3 className="font-medium mb-2">Cost Breakdown</h3>
                <div className="bg-gray-50 p-3 rounded border border-gray-200">
                  <div className="grid grid-cols-2 gap-y-2 text-sm">
                    <span className="text-gray-600">Subtotal:</span>
                    <span className="text-right">{formatCurrency(booking.subtotal || 0)}</span>

                    {/* Delivery Options Dropdown in mobile view */}
                    <span className="text-gray-600">Delivery:</span>
                    <div className="text-right">
                      <select
                        value={booking.delivery?.option?.id || 'none'}
                        onChange={(e) => {
                          const optionId = e.target.value;
                          let selectedOption = null;
                          let fee = 0;

                          if (optionId !== 'none') {
                            selectedOption = systemSettings.deliveryOptions.find(opt => opt.id === optionId) || null;
                            fee = selectedOption?.fee || 0;
                          }

                          // Calculate new total with updated delivery fee
                          const subtotal = booking.subtotal || 0;
                          const discount = booking.discount || 0;
                          const subtotalAfterDiscount = subtotal - discount;
                          const tax = Math.round((subtotalAfterDiscount + fee) * (systemSettings.taxRate || 0) / 100 * 1000) / 1000;

                          const updatedBooking = {
                            ...booking,
                            delivery: {
                              option: selectedOption,
                              fee
                            },
                            deliveryFee: fee,
                            tax: tax
                          };

                          // Update booking in database
                          updateBooking(updatedBooking).then(() => {
                            // Update local state
                            const updatedBookings = bookings.map(b =>
                              b.id === booking.id ? updatedBooking : b
                            );
                            onBookingsUpdate(updatedBookings);
                          }).catch((error) => {
                            console.error('Error updating delivery option:', error);
                          });
                        }}
                        className="border border-gray-300 rounded py-1 px-2 text-sm w-full"
                      >
                        <option value="none">Self Pickup (Free)</option>
                        {systemSettings.deliveryOptions.map(opt => (
                          <option key={opt.id} value={opt.id}>
                            {opt.name} ({formatCurrency(opt.fee)})
                          </option>
                        ))}
                      </select>
                      <span className="block mt-1">{formatCurrency(booking.deliveryFee || 0)}</span>
                    </div>

                    <span className="text-gray-600">Discount:</span>
                    <span className="text-right">-{formatCurrency(booking.discount || 0)}</span>

                    <span className="text-gray-600">VAT ({systemSettings.taxRate || 0}%):</span>
                    <span className="text-right">{formatCurrency(booking.tax || 0)}</span>

                    <span className="text-gray-800 font-medium pt-2 border-t border-gray-200 mt-2">Total:</span>
                    <span className="text-right font-medium pt-2 border-t border-gray-200 mt-2">{formatCurrency(calculateBookingTotal(booking))}</span>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="mb-4 flex flex-wrap gap-2">
                <button
                  onClick={() => {
                    setEditingDiscount({ ...booking });
                  }}
                  className="px-3 py-1 bg-purple-600 text-white rounded-md text-sm flex items-center"
                >
                  <Edit size={14} className="mr-1" /> Add/Edit Discount
                </button>
                <button
                  onClick={() => {
                    setEditingDates({ ...booking });
                    setShowDatePickerModal(true);
                  }}
                  className="px-3 py-1 bg-indigo-600 text-white rounded-md text-sm flex items-center"
                >
                  <Calendar size={14} className="mr-1" /> Edit Dates
                </button>
                <button
                  onClick={() => handleEditItems(booking)}
                  className="px-3 py-1 bg-blue-600 text-white rounded-md text-sm flex items-center"
                >
                  <Edit size={14} className="mr-1" /> Edit Items
                </button>
              </div>

              {/* Payment Section */}
              <div className="mb-4">
                <div className="flex justify-between items-center mb-2">
                  <h3 className="font-medium">Payment History</h3>
                  <button
                    onClick={() => handleLocalAddPayment(booking.id)}
                    className="px-2 py-1 bg-green-600 text-white rounded-md text-xs flex items-center"
                  >
                    <Plus size={14} className="mr-1" /> Add Payment
                  </button>
                </div>

                {(!booking.payments || booking.payments.length === 0) ? (
                  <p className="text-gray-500 text-sm">No payments recorded yet.</p>
                ) : (
                  <div className="space-y-2">
                    {booking.payments.map(payment => (
                      <div key={payment.id} className="bg-white p-3 rounded border border-gray-200 text-sm">
                        <div className="flex justify-between items-center mb-2">
                          <div className="flex items-center">
                            {payment.method === 'cash' ? (
                              <DollarSign size={14} className="mr-2 text-green-500" />
                            ) : payment.method === 'card' ? (
                              <CreditCard size={14} className="mr-2 text-blue-500" />
                            ) : (
                              <Building size={14} className="mr-2 text-purple-500" />
                            )}
                            <span className="capitalize">{payment.method}</span>
                          </div>
                          <span className="font-medium">{formatCurrency(payment.amount)}</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-gray-500">{formatDate(payment.date)}</span>
                          <div className="flex space-x-2">
                            <button
                              onClick={() => handleLocalEditPayment(booking.id, payment.id)}
                              className="p-1 rounded-full hover:bg-gray-100 text-gray-600"
                            >
                              <Edit size={14} />
                            </button>
                            <button
                              onClick={() => onDeletePayment(booking.id, payment.id)}
                              className="p-1 rounded-full hover:bg-gray-100 text-red-600"
                            >
                              <Trash2 size={14} />
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Booking Items Section */}
              <div>
                <div className="flex justify-between items-center mb-2">
                  <h3 className="font-medium">Booking Items</h3>
                </div>

                <div className="space-y-2">
                  {booking.products.map(product => (
                    <div key={product.id} className="bg-white p-3 rounded border border-gray-200">
                      <div className="flex justify-between items-center">
                        <div className="flex items-center">
                          <span className="font-medium mr-1">{product.quantity}x</span>
                          <span>{product.name}</span>
                        </div>
                        <span className="font-medium">
                          {formatCurrency((product.temporaryDailyRate || product.dailyRate) *
                            product.quantity * (product.customDays || booking.rentalPeriod.days))}
                        </span>
                      </div>
                      <div className="text-sm text-gray-500 mt-1">
                        <span>{formatCurrency(product.temporaryDailyRate || product.dailyRate)}/day</span>
                        <span className="mx-1">•</span>
                        <span>{product.customDays || booking.rentalPeriod.days} {(product.customDays || booking.rentalPeriod.days) === 1 ? 'day' : 'days'}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  };



  const handleSaveDates = (dates: string[], rentalType: 'daily' | 'weekly') => {
    if (editingDates) {
      const updatedBooking = {
        ...editingDates,
        rentalPeriod: {
          ...editingDates.rentalPeriod,
          dates,
          days: dates.length,
          rentalType
        }
      };

      // Update booking in database
      updateBooking(updatedBooking).then(() => {
        // Update the local state
        const updatedBookings = bookings.map(b =>
          b.id === updatedBooking.id ? updatedBooking : b
        );
        onBookingsUpdate(updatedBookings);
        setEditingDates(null);
        setShowDatePickerModal(false);
      }).catch((error) => {
        console.error('Error updating dates:', error);
        alert('Failed to update dates. Please try again.');
      });
    }
  };

  return (
    <div>
      <div className="bg-white rounded-lg shadow overflow-hidden">
        {/* Desktop view - table */}
        <div className="hidden md:block overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Quote / Customer
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Rental Period
                </th>
                <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Payment Status
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Total Amount
                </th>
                <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-32">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {bookings.map(booking => renderDesktopBookingRow(booking))}
            </tbody>
          </table>
        </div>

        {/* Mobile view - cards */}
        <div className="md:hidden p-4 space-y-4">
          {bookings.map(booking => renderMobileBookingCard(booking))}
        </div>

        {/* Expanded booking details (shown for desktop only, mobile has inline expansion) */}
        <div className="hidden md:block">
          {bookings.map(booking => (
            expandedBookingId === booking.id && (
              <div key={`detail-${booking.id}`} className="bg-gray-50 border-t border-gray-200 p-4">
                {/* Status Change */}
                <div className="mb-6">
                  <h3 className="text-lg font-medium mb-2">Booking Status</h3>
                  <div className="flex flex-wrap gap-2">
                    {['pending', 'confirmed', 'completed', 'cancelled'].map(status => (
                      <button
                        key={status}
                        onClick={() => onStatusChange(booking.id, status as any)}
                        className={`px-4 py-2 rounded-md text-sm font-medium ${
                          booking.status === status
                            ? 'bg-blue-600 text-white'
                            : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                        } capitalize`}
                      >
                        {status}
                      </button>
                    ))}
                  </div>
                </div>

                {/* Payment Section */}
                <div className="mb-6">
                  <div className="flex justify-between items-center mb-2">
                    <h3 className="text-lg font-medium">Payment History</h3>
                    <button
                      onClick={() => handleLocalAddPayment(booking.id)}
                      className="px-3 py-1 bg-green-600 text-white rounded-md text-sm flex items-center"
                    >
                      <Plus size={16} className="mr-1" /> Add Payment
                    </button>
                  </div>

                  {(!booking.payments || booking.payments.length === 0) ? (
                    <p className="text-gray-500 text-sm">No payments recorded yet.</p>
                  ) : (
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Date
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Method
                            </th>
                            <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Amount
                            </th>
                            <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Actions
                            </th>
                          </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-200">
                          {booking.payments.map(payment => (
                            <tr key={payment.id}>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="text-sm text-gray-900">{formatDate(payment.date)}</div>
                                <div className="text-xs text-gray-500">{payment.transactionId}</div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="flex items-center">
                                  {payment.method === 'cash' ? (
                                    <DollarSign size={16} className="mr-2 text-green-500" />
                                  ) : (
                                    <CreditCard size={16} className="mr-2 text-blue-500" />
                                  )}
                                  <span className="text-sm capitalize">{payment.method}</span>
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-right">
                                <div className="text-sm font-medium text-gray-900">{formatCurrency(payment.amount)}</div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-center">
                                <div className="flex items-center justify-center space-x-2">
                                  <button
                                    onClick={() => handleLocalEditPayment(booking.id, payment.id)}
                                    className="p-1 rounded-full hover:bg-gray-100 text-gray-600"
                                  >
                                    <Edit size={16} />
                                  </button>
                                  <button
                                    onClick={() => onDeletePayment(booking.id, payment.id)}
                                    className="p-1 rounded-full hover:bg-gray-100 text-red-600"
                                  >
                                    <Trash2 size={14} />
                                  </button>
                                </div>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  )}
                </div>

                {/* Cost Breakdown Section */}
                <div className="mb-6">
                  <h3 className="text-lg font-medium mb-2">Cost Breakdown</h3>
                  <div className="bg-white p-4 rounded shadow-sm border border-gray-200 max-w-md">
                    <div className="grid grid-cols-2 gap-y-2">
                      <span className="text-gray-600">Subtotal:</span>
                      <span className="text-right">{formatCurrency(booking.subtotal || 0)}</span>

                      {/* Delivery Options Dropdown in mobile view */}
                      <span className="text-gray-600">Delivery:</span>
                      <div className="text-right">
                        <select
                          value={booking.delivery?.option?.id || 'none'}
                          onChange={(e) => {
                            const optionId = e.target.value;
                            let selectedOption = null;
                            let fee = 0;

                            if (optionId !== 'none') {
                              selectedOption = systemSettings.deliveryOptions.find(opt => opt.id === optionId) || null;
                              fee = selectedOption?.fee || 0;
                            }

                            // Calculate new total with updated delivery fee
                            const subtotal = booking.subtotal || 0;
                            const discount = booking.discount || 0;
                            const subtotalAfterDiscount = subtotal - discount;
                            const tax = Math.round((subtotalAfterDiscount + fee) * (systemSettings.taxRate || 0) / 100 * 1000) / 1000;

                            const updatedBooking = {
                              ...booking,
                              delivery: {
                                option: selectedOption,
                                fee
                              },
                              deliveryFee: fee,
                              tax: tax
                            };

                            // Update booking in database
                            updateBooking(updatedBooking).then(() => {
                              // Update local state
                              const updatedBookings = bookings.map(b =>
                                b.id === booking.id ? updatedBooking : b
                              );
                              onBookingsUpdate(updatedBookings);
                            }).catch((error) => {
                              console.error('Error updating delivery option:', error);
                            });
                          }}
                          className="border border-gray-300 rounded py-1 px-2 text-sm w-full"
                        >
                          <option value="none">Self Pickup (Free)</option>
                          {systemSettings.deliveryOptions.map(opt => (
                            <option key={opt.id} value={opt.id}>
                              {opt.name} ({formatCurrency(opt.fee)})
                            </option>
                          ))}
                        </select>
                        <span className="block mt-1">{formatCurrency(booking.deliveryFee || 0)}</span>
                      </div>

                      <span className="text-gray-600">Discount:</span>
                      <span className="text-right">-{formatCurrency(booking.discount || 0)}</span>

                      <span className="text-gray-600">VAT ({systemSettings.taxRate || 0}%):</span>
                      <span className="text-right">{formatCurrency(booking.tax || 0)}</span>

                      <span className="text-gray-800 font-medium pt-2 border-t border-gray-200 mt-2">Total:</span>
                      <span className="text-right font-medium pt-2 border-t border-gray-200 mt-2">{formatCurrency(calculateBookingTotal(booking))}</span>
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="mb-4 flex space-x-2 flex-wrap">
                  <button
                    onClick={() => {
                      setEditingDiscount({ ...booking });
                    }}
                    className="px-3 py-1 bg-purple-600 text-white rounded-md text-sm flex items-center mb-2"
                  >
                    <Edit size={14} className="mr-1" /> Add/Edit Discount
                  </button>
                  <button
                    onClick={() => {
                      setEditingDates({ ...booking });
                      setShowDatePickerModal(true);
                    }}
                    className="px-3 py-1 bg-indigo-600 text-white rounded-md text-sm flex items-center mb-2"
                  >
                    <Calendar size={14} className="mr-1" /> Edit Dates
                  </button>
                  <button
                    onClick={() => handleEditItems(booking)}
                    className="px-3 py-1 bg-blue-600 text-white rounded-md text-sm flex items-center mb-2"
                  >
                    <Edit size={14} className="mr-1" /> Edit Items
                  </button>
                  <button
                    onClick={() => handleChangeClient(booking)}
                    className="px-3 py-1 bg-green-600 text-white rounded-md text-sm flex items-center mb-2"
                  >
                    <UserCog size={14} className="mr-1" /> Change Client
                  </button>
                </div>

                {/* Booking Items Section */}
                <div>
                  <div className="flex justify-between items-center mb-3">
                    <h3 className="text-lg font-medium">Booking Items</h3>
                  </div>

                  <BookingItems
                    booking={booking}
                    products={products}
                    systemSettings={systemSettings}
                    formatCurrency={formatCurrency}
                    onUpdate={(updatedBooking: Booking) => {
                      // Update the booking in the database
                      updateBooking(updatedBooking).then(() => {
                        // Update the local state
                        const updatedBookings = bookings.map(b =>
                          b.id === updatedBooking.id ? updatedBooking : b
                        );
                        onBookingsUpdate(updatedBookings);
                      }).catch((error: Error) => {
                        console.error('Error updating booking items:', error);
                        alert(`Failed to update booking items: ${error.message}`);
                      });
                    }}
                  />
                </div>
              </div>
            )
          ))}
        </div>
      </div>

      {/* Client change modal */}
      {editingClient && (
        <ClientEditModal
          booking={editingClient}
          onClose={() => setEditingClient(null)}
          onSave={handleSaveClientChanges}
          onClientChange={handleClientSelect}
        />
      )}

      {/* Edit booking items modal */}
      {editingBooking && (
        <EditBookingItemsModal
          booking={editingBooking}
          products={products}
          systemSettings={systemSettings}
          formatCurrency={formatCurrency}
          onClose={() => setEditingBooking(null)}
          onSave={handleSaveBookingChanges}
          onBookingUpdate={setEditingBooking}
        />
      )}

      {/* Date Picker Modal */}
      {editingDates && (
        <DatePickerModal
          show={showDatePickerModal}
          onClose={() => setShowDatePickerModal(false)}
          booking={editingDates}
          onSave={handleSaveDates}
        />
      )}

      {/* Discount Modal */}
      {editingDiscount && (
        <DiscountModal
          booking={editingDiscount}
          onClose={() => setEditingDiscount(null)}
          onSave={(updatedBooking) => {
            // Update booking in database
            updateBooking(updatedBooking).then(() => {
              // Update the local state
              const updatedBookings = bookings.map(b =>
                b.id === updatedBooking.id ? updatedBooking : b
              );
              onBookingsUpdate(updatedBookings);
              setEditingDiscount(null);
            }).catch((error) => {
              console.error('Error updating discount:', error);
              alert('Failed to update discount. Please try again.');
            });
          }}
          formatCurrency={formatCurrency}
          systemSettings={systemSettings}
        />
      )}

      {/* Payment Modal */}
      {showPaymentModal && (
        <PaymentModal
          show={showPaymentModal}
          isEditing={!!editingPaymentId}
          payment={newPayment}
          onClose={() => {
            setShowPaymentModal(false);
            setEditingPaymentId(null);
          }}
          onSave={handleSubmitPayment}
          onPaymentChange={(field, value) => {
            setNewPayment({
              ...newPayment,
              [field]: value
            });
          }}
          bookingId={paymentBookingId || undefined}
          calculateRemainingAmount={(bookingId) => {
            const booking = bookings.find(b => b.id === bookingId);
            if (!booking) return 0;

            // Calculate the remaining amount
            const calculatedTotal = calculateBookingTotal(booking);
            const paidAmount = (booking.payments || []).reduce((sum, payment) => sum + payment.amount, 0);
            return calculatedTotal - paidAmount;
          }}
        />
      )}

      {/* Date Picker Modal */}
      {editingDates && (
        <DatePickerModal
          show={showDatePickerModal}
          onClose={() => setShowDatePickerModal(false)}
          booking={editingDates}
          onSave={handleSaveDates}
        />
      )}
    </div>
  );
};

export default BookingList;