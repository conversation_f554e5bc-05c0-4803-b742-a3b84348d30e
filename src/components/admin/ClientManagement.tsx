import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Search, User, Mail, Phone, MapPin, Plus, Pencil, Trash2, X, ChevronDown, ChevronUp, Calendar, DollarSign, FileText, BarChart2, Users, Info, CreditCard, ChevronLeft, ChevronRight } from 'lucide-react';
import { Booking, Client, ClientActivity, ClientStatistics } from '../../types';
import { clients as initialClients, clientActivities as initialClientActivities, clientStatistics as initialClientStatistics } from '../../data/clients';
import { Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { addClient, updateClient, deleteClient, getClients, addClientActivity, mergeClients } from '../../services/firebase';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

interface ClientManagementProps {
  bookings: Booking[];
  onBookingsUpdate: (bookings: Booking[]) => void;
}

const ClientManagement: React.FC<ClientManagementProps> = ({ bookings, onBookingsUpdate }) => {
  const [clients, setClients] = useState<Client[]>([]);
  const [clientActivities, setClientActivities] = useState<ClientActivity[]>([]);
  const [clientStatistics, setClientStatistics] = useState<ClientStatistics>(initialClientStatistics);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [expandedClientId, setExpandedClientId] = useState<string | null>(null);
  const [showAddClientModal, setShowAddClientModal] = useState(false);
  const [editingClient, setEditingClient] = useState<Client | null>(null);
  const [showMergeModal, setShowMergeModal] = useState(false);
  const [clientsToMerge, setClientsToMerge] = useState<{
    primary: string | null;
    secondary: string | null;
  }>({
    primary: null,
    secondary: null
  });
  const [clientNote, setClientNote] = useState('');
  const [newClient, setNewClient] = useState<Partial<Client>>({
    name: '',
    email: '',
    phone: '',
    address: '',
    notes: ''
  });
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  // Load client data from Firebase on initial render
  useEffect(() => {
    const loadClients = async () => {
      try {
        const clientsData = await getClients();
        setClients(clientsData);
      } catch (error) {
        console.error('Error loading clients:', error);
      }
    };
    loadClients();
  }, []);

  // Memoize the updateClientStatistics function to prevent it from being recreated on every render
  const updateClientStatistics = useCallback(() => {
    // Calculate total clients
    const totalClients = clients.length;

    // Calculate new clients this month
    const currentDate = new Date();
    const firstDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
    const newClientsThisMonth = clients.filter(client =>
      new Date(client.dateAdded) >= firstDayOfMonth
    ).length;

    // Calculate active clients (clients with bookings in the last 3 months)
    const threeMonthsAgo = new Date();
    threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);
    const activeClients = clients.filter(client =>
      client.lastBooking && new Date(client.lastBooking) >= threeMonthsAgo
    ).length;

    // Calculate top clients by total spent
    const topClients = [...clients]
      .sort((a, b) => b.totalSpent - a.totalSpent)
      .slice(0, 3)
      .map(client => ({
        clientId: client.id,
        name: client.name,
        totalSpent: client.totalSpent
      }));

    // Use existing monthly revenue data
    const monthlyRevenue = initialClientStatistics.monthlyRevenue;

    const updatedStatistics: ClientStatistics = {
      totalClients,
      newClientsThisMonth,
      activeClients,
      topClients,
      monthlyRevenue
    };

    setClientStatistics(updatedStatistics);
  }, [clients]);

  // Update client statistics whenever clients change
  useEffect(() => {
    if (clients.length > 0) {
      updateClientStatistics();
    }
  }, [clients, updateClientStatistics]);

  // Update this function to calculate pending payments, total spent, and total bookings
  const updateClientsPendingPayments = useCallback(() => {
    if (clients.length === 0 || bookings.length === 0) return;

    const updatedClients = clients.map(client => {
      // Find all bookings for this client by email
      const clientBookings = bookings.filter(booking =>
        booking.customer.email.toLowerCase() === client.email.toLowerCase()
      );

      // Calculate total pending payments
      let pendingPayment = 0;
      let totalSpent = 0;

      clientBookings.forEach(booking => {
        const totalAmount = booking.totalAmount || 0;
        const paidAmount = (booking.payments || []).reduce((sum, payment) => sum + payment.amount, 0);
        pendingPayment += Math.max(0, totalAmount - paidAmount);
        totalSpent += paidAmount; // Total spent is the sum of all payments made
      });

      // Ensure dateAdded is a valid date, fallback to current date if invalid
      const dateAdded = client.dateAdded && new Date(client.dateAdded).toString() !== 'Invalid Date'
        ? client.dateAdded
        : new Date().toISOString();

      // Set the last booking date to the most recent booking date
      const lastBooking = clientBookings.length > 0
        ? [...clientBookings].sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())[0].date
        : client.lastBooking;

      return {
        ...client,
        pendingPayment: pendingPayment > 0 ? pendingPayment : undefined,
        totalSpent: totalSpent, // Update the total spent
        totalBookings: clientBookings.length, // Update the total bookings
        lastBooking: lastBooking,
        dateAdded: dateAdded
      };
    });

    // Only update state if there's an actual difference to avoid infinite loop
    const hasChanges = JSON.stringify(updatedClients) !== JSON.stringify(clients);
    if (hasChanges) {
      setClients(updatedClients);
    }
  }, [clients, bookings]);

  // Update clients with pending payment information from bookings
  // Use a ref to track if this is the first render to avoid infinite loop
  const isFirstRender = React.useRef(true);
  useEffect(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }

    if (clients.length > 0 && bookings.length > 0) {
      updateClientsPendingPayments();
    }
  }, [bookings, updateClientsPendingPayments]);

  // Add this useEffect to update client data in Firebase when it changes
  useEffect(() => {
    const saveClientUpdates = async () => {
      if (isFirstRender.current || clients.length === 0) return;

      try {
        // Update each client in database to persist total spent, booking count, and date fixes
        for (const client of clients) {
          // Skip clients that don't have an ID yet
          if (!client.id) continue;

          // Create a copy of the client object without undefined values
          const clientToUpdate = { ...client };

          // Remove undefined fields as Firestore doesn't support them
          if (clientToUpdate.pendingPayment === undefined) {
            delete clientToUpdate.pendingPayment;
          }

          // Make sure all required fields exist and have valid types
          // Use a type-safe approach to remove undefined fields
          const clientToSave: Record<string, any> = {};

          // Copy all fields that are not undefined
          Object.entries(clientToUpdate).forEach(([key, value]) => {
            if (value !== undefined) {
              clientToSave[key] = value;
            }
          });

          // Pass the sanitized client object to updateClient
          await updateClient(clientToSave as Client);
        }
      } catch (error) {
        console.error('Error updating clients in Firebase:', error);
      }
    };

    // Debounce the Firebase updates to avoid excessive writes
    const timer = setTimeout(saveClientUpdates, 2000);
    return () => clearTimeout(timer);
  }, [clients]);

  // Filter clients based on search term and status filter
  const filteredClients = useMemo(() => {
    return clients.filter(client => {
      if (!searchTerm) return true;

      const searchLower = searchTerm.toLowerCase();
      return (
        client.name.toLowerCase().includes(searchLower) ||
        client.email.toLowerCase().includes(searchLower) ||
        client.phone.toLowerCase().includes(searchLower) ||
        client.address.toLowerCase().includes(searchLower)
      );
    });
  }, [clients, searchTerm]);

  // Add this for pagination
  const paginatedClients = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return filteredClients.slice(startIndex, startIndex + itemsPerPage);
  }, [filteredClients, currentPage]);

  // Add this for handling page changes
  const totalPages = Math.ceil(filteredClients.length / itemsPerPage);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    setExpandedClientId(null);
    window.scrollTo(0, 0);
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'BHD',
      minimumFractionDigits: 3,
      maximumFractionDigits: 3
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString: string) => {
    if (!dateString) return 'Not available';

    const date = new Date(dateString);
    if (date.toString() === 'Invalid Date') return 'Not available';

    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Handle adding a new client
  const handleAddClient = async () => {
    if (!newClient.name || !newClient.email || !newClient.phone) {
      alert('Please fill in all required fields');
      return;
    }

    // Check if email already exists
    if (clients.some(client => client.email.toLowerCase() === newClient.email?.toLowerCase())) {
      alert('A client with this email already exists');
      return;
    }

    try {
      const clientToAdd = {
      name: newClient.name || '',
      email: newClient.email || '',
      phone: newClient.phone || '',
      address: newClient.address || '',
      dateAdded: new Date().toISOString(),
      totalBookings: 0,
      totalSpent: 0,
      notes: newClient.notes
    };

      const newClientId = await addClient(clientToAdd);
      const newClientWithId = { ...clientToAdd, id: newClientId };
      setClients([...clients, newClientWithId]);

    // Reset form and close modal
    setNewClient({
      name: '',
      email: '',
      phone: '',
      address: '',
      notes: ''
    });
    setShowAddClientModal(false);
    } catch (error) {
      console.error('Error adding client:', error);
      alert('Failed to add client. Please try again.');
    }
  };

  // Handle editing a client
  const handleEditClient = (client: Client) => {
    setEditingClient(client);
    setNewClient({
      name: client.name,
      email: client.email,
      phone: client.phone,
      address: client.address,
      notes: client.notes
    });
    setShowAddClientModal(true);
  };

  // Handle updating a client
  const handleUpdateClient = async () => {
    if (!editingClient || !newClient.name || !newClient.email || !newClient.phone) {
      alert('Please fill in all required fields');
      return;
    }

    // Check if email already exists (excluding the current client)
    if (clients.some(client =>
      client.email.toLowerCase() === newClient.email?.toLowerCase() &&
      String(client.id) !== String(editingClient.id)
    )) {
      alert('A client with this email already exists');
      return;
    }

    try {
      const updatedClient = {
        ...editingClient,
        name: newClient.name || editingClient.name,
        email: newClient.email || editingClient.email,
        phone: newClient.phone || editingClient.phone,
        address: newClient.address || editingClient.address,
        notes: newClient.notes
      };

      console.log('Updating client with ID:', updatedClient.id);
      await updateClient(updatedClient);
      setClients(clients.map(client =>
        String(client.id) === String(editingClient.id) ? updatedClient : client
      ));

    // Update bookings with the new client information
    const updatedBookings = bookings.map(booking => {
      if (booking.customer.email.toLowerCase() === editingClient.email.toLowerCase()) {
        return {
          ...booking,
          customer: {
            ...booking.customer,
            name: newClient.name || booking.customer.name,
            email: newClient.email || booking.customer.email,
            phone: newClient.phone || booking.customer.phone,
            address: newClient.address || booking.customer.address
          }
        };
      }
      return booking;
    });

    onBookingsUpdate(updatedBookings);

    // Reset form and close modal
    setEditingClient(null);
    setNewClient({
      name: '',
      email: '',
      phone: '',
      address: '',
      notes: ''
    });
    setShowAddClientModal(false);
    } catch (error) {
      console.error('Error updating client:', error);
      alert('Failed to update client. Please try again.');
    }
  };

  // Handle deleting a client
  const handleDeleteClient = async (clientId: string | number) => {
    console.log('Attempting to delete client:', clientId);

    // Find the client first to get the email
    // With the new structure, the clientId should be the email
    const clientToDelete = clients.find(c => String(c.id) === String(clientId));
    if (!clientToDelete) {
      console.error('Client not found with ID:', clientId);
      return;
    }

    console.log('Found client to delete:', clientToDelete);

    // Check if client has bookings
    const clientBookings = bookings.filter(booking =>
      booking.customer.email.toLowerCase() === clientToDelete.email.toLowerCase()
    );

    if (clientBookings.length > 0) {
      if (!window.confirm(`This client has ${clientBookings.length} bookings. Deleting the client will not delete the bookings. Continue?`)) {
        return;
      }
    }

    try {
      console.log('Calling deleteClient with email:', clientToDelete.email);
      await deleteClient(clientToDelete.email);
      console.log('Client deleted successfully');

      // Update local state
      setClients(prevClients => {
        const updatedClients = prevClients.filter(client => client.email !== clientToDelete.email);
        console.log('Updated clients list:', updatedClients);
        return updatedClients;
      });

      setClientActivities(prevActivities =>
        prevActivities.filter(activity => activity.clientId !== clientToDelete.id)
      );
    } catch (error) {
      console.error('Error deleting client:', error);
      alert('Failed to delete client. Please try again.');
    }
  };

  // Handle adding a note to a client
  const handleAddNote = async (clientId: string) => {
    if (!clientNote.trim()) return;

    const client = clients.find(c => c.id === clientId);
    if (!client) return;

    try {
    // Create a new activity for the note
      const newActivity: Omit<ClientActivity, 'id'> = {
      clientId,
      date: new Date().toISOString(),
      type: 'note',
      description: clientNote
    };

      // Add the activity to Firebase
      await addClientActivity(newActivity);

    // Update client notes
      const updatedClient = {
        ...client,
        notes: client.notes ? `${client.notes}\n\n${clientNote}` : clientNote
      };

      await updateClient(updatedClient);
      setClients(clients.map(c =>
        c.id === clientId ? updatedClient : c
      ));

    // Reset note input
    setClientNote('');
    } catch (error) {
      console.error('Error adding note:', error);
      alert('Failed to add note. Please try again.');
    }
  };

  // Handle merging clients
  const handleMergeClients = async () => {
    if (!clientsToMerge.primary || !clientsToMerge.secondary) return;

    const primaryClient = clients.find(c => String(c.id) === String(clientsToMerge.primary));
    const secondaryClient = clients.find(c => String(c.id) === String(clientsToMerge.secondary));

    if (!primaryClient || !secondaryClient) {
      console.error('Could not find one or both clients:', { primaryClient, secondaryClient });
      return;
    }

    try {
      console.log('Starting merge process:', {
        primary: {
          id: primaryClient.id,
          email: primaryClient.email,
          name: primaryClient.name
        },
        secondary: {
          id: secondaryClient.id,
          email: secondaryClient.email,
          name: secondaryClient.name
        }
      });

      // First, update all bookings to use the primary client's information
    const updatedBookings = bookings.map(booking => {
      if (booking.customer.email.toLowerCase() === secondaryClient.email.toLowerCase()) {
        return {
          ...booking,
          customer: {
            ...booking.customer,
            name: primaryClient.name,
            email: primaryClient.email,
            phone: primaryClient.phone,
            address: primaryClient.address
          }
        };
      }
      return booking;
    });

      // Update bookings first
    onBookingsUpdate(updatedBookings);

      // Then merge the clients in Firebase
      // With the new structure, the client ID is the email, so we can use the IDs directly
      await mergeClients(primaryClient.id, secondaryClient.id);

      // Update local state
      setClients(prevClients => {
        const updatedClients = prevClients.filter(client =>
          client.email.toLowerCase() !== secondaryClient.email.toLowerCase()
        );
        console.log('Updated clients list after merge:', updatedClients);
        return updatedClients;
      });

      // Update client activities
      setClientActivities(prevActivities => {
        const updatedActivities = prevActivities.map(activity => {
          if (activity.clientId === secondaryClient.id) {
            return {
              ...activity,
              clientId: primaryClient.id
            };
          }
          return activity;
        });
        return updatedActivities;
      });

      // Reset merge modal
    setShowMergeModal(false);
      setClientsToMerge({ primary: null, secondary: null });

      console.log('Merge completed successfully');
    } catch (error) {
      console.error('Error merging clients:', error);
      alert('Failed to merge clients. Please try again.');
    }
  };

  // Get client activities
  const getClientActivities = (clientId: string) => {
    // Get all activities for the client
    const activities = clientActivities
      .filter(activity => activity.clientId === clientId)
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

    // Get all bookings for the client
    const clientBookings = bookings
      .filter(booking => booking.customer.email.toLowerCase() === clients.find(c => c.id === clientId)?.email.toLowerCase())
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

    // Convert bookings to activities
    const bookingActivities = clientBookings.map(booking => ({
      id: `booking-${booking.id}`,
      clientId,
      date: booking.date,
      type: 'booking' as const,
      description: `Booking #${booking.quoteNumber}: ${booking.products.map(p => p.name).join(', ')}`,
      amount: booking.totalAmount,
      bookingId: booking.id,
      quoteNumber: booking.quoteNumber
    }));

    // Combine and sort all activities
    return [...activities, ...bookingActivities]
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
  };

  // Memoize chart data to prevent unnecessary re-renders
  const chartData = useMemo(() => ({
    labels: clientStatistics.monthlyRevenue.map(item => item.month),
    datasets: [
      {
        label: 'Monthly Revenue',
        data: clientStatistics.monthlyRevenue.map(item => item.revenue),
        backgroundColor: 'rgba(59, 130, 246, 0.5)',
        borderColor: 'rgb(59, 130, 246)',
        borderWidth: 1
      }
    ]
  }), [clientStatistics.monthlyRevenue]);

  // Memoize chart options to prevent unnecessary re-renders
  const chartOptions = useMemo(() => ({
    responsive: true,
    maintainAspectRatio: true,
    animation: {
      duration: 0 // Disable animations
    },
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: 'Monthly Revenue'
      }
    },
    scales: {
      y: {
        beginAtZero: true
      }
    }
  }), []);

  return (
    <div>
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
        <h2 className="text-xl font-bold">Client Management</h2>
        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search size={16} className="text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search clients..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-md w-full sm:w-auto"
            />
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => setShowAddClientModal(true)}
              className="px-4 py-2 bg-blue-600 text-white rounded-md flex items-center"
            >
              <Plus size={18} className="mr-2" />
              Add Client
            </button>
            <button
              onClick={() => setShowMergeModal(true)}
              className="px-4 py-2 bg-green-600 text-white rounded-md flex items-center"
            >
              <Users size={18} className="mr-2" />
              Merge Clients
            </button>
          </div>
        </div>
      </div>

      {/* Client Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div className="bg-white p-4 rounded-lg shadow border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-500 text-sm">Total Clients</p>
              <p className="text-2xl font-bold">{clientStatistics.totalClients}</p>
            </div>
            <div className="p-3 bg-blue-100 rounded-full text-blue-600">
              <Users size={24} />
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-500 text-sm">New Clients (This Month)</p>
              <p className="text-2xl font-bold">{clientStatistics.newClientsThisMonth}</p>
            </div>
            <div className="p-3 bg-green-100 rounded-full text-green-600">
              <User size={24} />
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-500 text-sm">Active Clients</p>
              <p className="text-2xl font-bold">{clientStatistics.activeClients}</p>
            </div>
            <div className="p-3 bg-purple-100 rounded-full text-purple-600">
              <User size={24} />
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-500 text-sm">Top Client</p>
              <p className="text-2xl font-bold">{clientStatistics.topClients[0]?.name.split(' ')[0] || 'None'}</p>
            </div>
            <div className="p-3 bg-yellow-100 rounded-full text-yellow-600">
              <DollarSign size={24} />
            </div>
          </div>
        </div>
      </div>

      {/* Revenue Chart */}
      <div className="bg-white p-4 rounded-lg shadow border border-gray-200 mb-6">
        <h3 className="text-lg font-semibold mb-4">Revenue Overview</h3>
        <div className="h-64">
          <Bar data={chartData} options={chartOptions} />
        </div>
      </div>

      {/* Clients List */}
      <div className="bg-white rounded-lg shadow border border-gray-200 overflow-hidden">
        {/* Mobile-friendly client cards */}
        <div className="block lg:hidden">
          {paginatedClients.length > 0 ? (
            <div className="divide-y divide-gray-200">
              {paginatedClients.map((client, index) => (
                <div key={`mobile-client-${client.id}-${index}`} className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="h-10 w-10 bg-gray-200 rounded-full flex items-center justify-center">
                        <User size={20} />
                      </div>
                      <div className="ml-3">
                        <div className="text-sm font-medium text-gray-900">{client.name}</div>
                        <div className="text-xs text-gray-500">Since {formatDate(client.dateAdded)}</div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-1">
                      <button
                        onClick={() => setExpandedClientId(expandedClientId === client.id ? null : client.id)}
                        className="p-1 rounded-full hover:bg-gray-100"
                      >
                        {expandedClientId === client.id ? (
                          <ChevronUp size={18} />
                        ) : (
                          <ChevronDown size={18} />
                        )}
                      </button>
                      <button
                        onClick={() => handleEditClient(client)}
                        className="p-1 rounded-full hover:bg-gray-100 text-blue-600"
                      >
                        <Pencil size={18} />
                      </button>
                      <button
                        onClick={() => handleDeleteClient(client.id)}
                        className="p-1 rounded-full hover:bg-gray-100 text-red-600"
                      >
                        <Trash2 size={18} />
                      </button>
                    </div>
                  </div>

                  <div className="mt-3 grid grid-cols-2 gap-2">
                    <div>
                      <div className="flex items-center text-sm text-gray-600">
                        <Mail size={14} className="text-gray-400 mr-1" />
                        <span className="truncate">{client.email}</span>
                      </div>
                      <div className="flex items-center text-sm text-gray-600 mt-1">
                        <Phone size={14} className="text-gray-400 mr-1" />
                        {client.phone}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium text-gray-900">
                        {formatCurrency(client.totalSpent)}
                      </div>
                      <div className="flex items-center justify-end mt-1">
                        <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                          {client.totalBookings} bookings
                        </span>
                      </div>
                      {client.pendingPayment && (
                        <div className="text-xs text-red-600 flex items-center justify-end mt-1">
                          <CreditCard size={12} className="mr-1" />
                          {formatCurrency(client.pendingPayment)} pending
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Expanded Client Details (Mobile) */}
                  {expandedClientId === client.id && (
                    <div className="mt-4 pt-3 border-t border-gray-200">
                      <div className="space-y-4">
                        {/* Client Details */}
                        <div className="bg-gray-50 p-3 rounded-lg">
                          <h4 className="font-medium text-gray-900 mb-2 flex items-center text-sm">
                            <User size={16} className="mr-1 text-blue-600" />
                            Client Details
                          </h4>
                          <div className="grid grid-cols-2 gap-2 text-sm">
                            <div>
                              <p className="text-xs text-gray-500">Address</p>
                              <p className="font-medium">{client.address}</p>
                            </div>
                            {client.lastBooking && (
                              <div>
                                <p className="text-xs text-gray-500">Last Booking</p>
                                <p className="font-medium">{formatDate(client.lastBooking)}</p>
                              </div>
                            )}
                          </div>
                        </div>

                        {/* Client Activity */}
                        <div className="bg-gray-50 p-3 rounded-lg">
                          <h4 className="font-medium text-gray-900 mb-2 flex items-center text-sm">
                            <Calendar size={16} className="mr-1 text-blue-600" />
                            Recent Activity
                          </h4>
                          <div className="space-y-2 max-h-48 overflow-y-auto">
                            {getClientActivities(client.id).slice(0, 3).map(activity => (
                              <div key={activity.id} className="border-l-2 border-blue-500 pl-2 py-1">
                                <div className="flex justify-between items-start">
                                  <p className="text-xs font-medium">
                                    {activity.type === 'booking' && 'New Booking'}
                                    {activity.type === 'payment' && 'Payment'}
                                    {activity.type === 'quote' && 'Quote'}
                                    {activity.type === 'note' && 'Note'}
                                  </p>
                                  <p className="text-xs text-gray-500">{formatDate(activity.date)}</p>
                                </div>
                                <p className="text-xs text-gray-600 truncate">{activity.description}</p>
                                {activity.amount && (
                                  <p className="text-xs font-medium text-blue-600">
                                    {formatCurrency(activity.amount)}
                                  </p>
                                )}
                              </div>
                            ))}
                            {getClientActivities(client.id).length === 0 && (
                              <p className="text-xs text-gray-500 italic">No recent activity</p>
                            )}
                          </div>
                        </div>

                        {/* Client Notes */}
                        <div className="bg-gray-50 p-3 rounded-lg">
                          <h4 className="font-medium text-gray-900 mb-2 flex items-center text-sm">
                            <FileText size={16} className="mr-1 text-blue-600" />
                            Notes
                          </h4>
                          <div className="mb-2">
                            <textarea
                              value={clientNote}
                              onChange={(e) => setClientNote(e.target.value)}
                              className="w-full p-2 border border-gray-300 rounded-md text-sm"
                              rows={2}
                              placeholder="Add a note..."
                            />
                            <button
                              onClick={() => handleAddNote(client.id)}
                              disabled={!clientNote.trim()}
                              className={`mt-1 px-2 py-1 rounded-md text-xs ${
                                clientNote.trim()
                                  ? 'bg-blue-600 text-white hover:bg-blue-700'
                                  : 'bg-gray-200 text-gray-500 cursor-not-allowed'
                              }`}
                            >
                              Add Note
                            </button>
                          </div>
                          <div className="max-h-20 overflow-y-auto bg-white p-2 rounded-md">
                            {client.notes ? (
                              <p className="text-xs whitespace-pre-line">{client.notes}</p>
                            ) : (
                              <p className="text-xs text-gray-500 italic">No notes available</p>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="p-6 text-center text-gray-500">
              No clients found matching your search criteria.
            </div>
          )}
        </div>

        {/* Desktop table view (keep the existing but use paginatedClients) */}
        <div className="hidden lg:block overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Client
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Contact
                </th>
                <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Bookings
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Total Spent
                </th>
                <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {paginatedClients.length > 0 ? (
                paginatedClients.map((client, index) => (
                  <React.Fragment key={`client-${client.id}-${index}`}>
                    <tr
                      className="border-b border-gray-200 hover:bg-gray-50"
                    >
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10 bg-gray-200 rounded-full flex items-center justify-center">
                            <User size={20} />
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">{client.name}</div>
                            <div className="text-sm text-gray-500">Added on {formatDate(client.dateAdded)}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 flex items-center">
                          <Mail size={16} className="text-gray-400 mr-2" />
                          {client.email}
                        </div>
                        <div className="text-sm text-gray-500 flex items-center mt-1">
                          <Phone size={16} className="text-gray-400 mr-2" />
                          {client.phone}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-center">
                        <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                          {client.totalBookings}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right">
                        <div className="text-sm font-medium text-gray-900">
                          {formatCurrency(client.totalSpent)}
                        </div>
                        {client.pendingPayment && (
                          <div className="text-sm text-red-600 flex items-center justify-end mt-1">
                            <CreditCard size={14} className="mr-1" />
                            {formatCurrency(client.pendingPayment)} pending
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-center">
                        <div className="flex items-center justify-center space-x-2">
                          <button
                            onClick={() => setExpandedClientId(expandedClientId === client.id ? null : client.id)}
                            className="p-1 rounded-full hover:bg-gray-100"
                            title={expandedClientId === client.id ? "Collapse" : "Expand"}
                          >
                            {expandedClientId === client.id ? (
                              <ChevronUp size={18} />
                            ) : (
                              <ChevronDown size={18} />
                            )}
                          </button>
                          <button
                            onClick={() => handleEditClient(client)}
                            className="p-1 rounded-full hover:bg-gray-100 text-blue-600"
                            title="Edit"
                          >
                            <Pencil size={18} />
                          </button>
                          <button
                            onClick={() => handleDeleteClient(client.id)}
                            className="p-1 rounded-full hover:bg-gray-100 text-red-600"
                            title="Delete"
                          >
                            <Trash2 size={18} />
                          </button>
                        </div>
                      </td>
                    </tr>

                    {/* Keep existing expanded view */}
                    {expandedClientId === client.id && (
                      <tr>
                        <td colSpan={5} className="px-6 py-4 bg-gray-50 border-t border-gray-200">
                          {/* Keep existing expanded view content */}
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                            {/* Client Details */}
                            <div className="bg-white p-4 rounded-lg shadow border border-gray-200">
                              <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                                <User size={18} className="mr-2 text-blue-600" />
                                Client Details
                              </h4>
                              <div className="space-y-3">
                                <div>
                                  <p className="text-sm text-gray-500">Full Name</p>
                                  <p className="font-medium">{client.name}</p>
                                </div>
                                <div>
                                  <p className="text-sm text-gray-500">Email</p>
                                  <p className="font-medium">{client.email}</p>
                                </div>
                                <div>
                                  <p className="text-sm text-gray-500">Phone</p>
                                  <p className="font-medium">{client.phone}</p>
                                </div>
                                <div>
                                  <p className="text-sm text-gray-500">Address</p>
                                  <p className="font-medium">{client.address}</p>
                                </div>
                                <div>
                                  <p className="text-sm text-gray-500">Client Since</p>
                                  <p className="font-medium">{formatDate(client.dateAdded)}</p>
                                </div>
                                {client.lastBooking && (
                                  <div>
                                    <p className="text-sm text-gray-500">Last Booking</p>
                                    <p className="font-medium">{formatDate(client.lastBooking)}</p>
                                  </div>
                                )}
                              </div>
                            </div>

                            {/* Client Activity */}
                            <div className="bg-white p-4 rounded-lg shadow border border-gray-200">
                              <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                                <Calendar size={18} className="mr-2 text-blue-600" />
                                Recent Activity
                              </h4>
                              <div className="space-y-3 max-h-64 overflow-y-auto">
                                {getClientActivities(client.id).slice(0, 5).map(activity => (
                                  <div key={activity.id} className="border-l-2 border-blue-500 pl-3 py-1">
                                    <div className="flex justify-between items-start">
                                      <p className="text-sm font-medium">
                                        {activity.type === 'booking' && 'New Booking'}
                                        {activity.type === 'payment' && 'Payment Received'}
                                        {activity.type === 'quote' && 'Quote Generated'}
                                        {activity.type === 'note' && 'Note Added'}
                                        {activity.type === 'merge' && 'Client Merged'}
                                      </p>
                                      <p className="text-xs text-gray-500">{formatDate(activity.date)}</p>
                                    </div>
                                    <p className="text-sm text-gray-600">{activity.description}</p>
                                    {activity.amount && (
                                      <p className="text-sm font-medium text-blue-600">
                                        {formatCurrency(activity.amount)}
                                      </p>
                                    )}
                                    {activity.quoteNumber && (
                                      <p className="text-xs text-gray-500">
                                        Quote: {activity.quoteNumber}
                                      </p>
                                    )}
                                  </div>
                                ))}
                                {getClientActivities(client.id).length === 0 && (
                                  <p className="text-sm text-gray-500 italic">No recent activity</p>
                                )}
                              </div>
                            </div>

                            {/* Client Notes */}
                            <div className="bg-white p-4 rounded-lg shadow border border-gray-200">
                              <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                                <FileText size={18} className="mr-2 text-blue-600" />
                                Notes
                              </h4>
                              <div className="mb-3">
                                <textarea
                                  value={clientNote}
                                  onChange={(e) => setClientNote(e.target.value)}
                                  className="w-full p-2 border border-gray-300 rounded-md text-sm"
                                  rows={3}
                                  placeholder="Add a note about this client..."
                                />
                                <button
                                  onClick={() => handleAddNote(client.id)}
                                  disabled={!clientNote.trim()}
                                  className={`mt-2 px-3 py-1 rounded-md text-sm ${
                                    clientNote.trim()
                                      ? 'bg-blue-600 text-white hover:bg-blue-700'
                                      : 'bg-gray-200 text-gray-500 cursor-not-allowed'
                                  }`}
                                >
                                  Add Note
                                </button>
                              </div>
                              <div className="max-h-32 overflow-y-auto bg-gray-50 p-3 rounded-md">
                                {client.notes ? (
                                  <p className="text-sm whitespace-pre-line">{client.notes}</p>
                                ) : (
                                  <p className="text-sm text-gray-500 italic">No notes available</p>
                                )}
                              </div>
                            </div>
                          </div>
                        </td>
                      </tr>
                    )}
                  </React.Fragment>
                ))
              ) : (
                <tr>
                  <td colSpan={5} className="px-6 py-8 text-center text-gray-500">
                    No clients found matching your search criteria.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center items-center mt-6 space-x-2">
          <button
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
            className={`p-2 rounded-md ${
              currentPage === 1
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                : 'bg-white text-gray-600 hover:bg-gray-50 border border-gray-300'
            }`}
          >
            <ChevronLeft size={20} />
          </button>

          <div className="flex items-center space-x-1">
            {Array.from({ length: totalPages }, (_, i) => i + 1)
              .filter(page => {
                // Show first page, last page, current page, and pages adjacent to current page
                return (
                  page === 1 ||
                  page === totalPages ||
                  Math.abs(page - currentPage) <= 1
                );
              })
              .map((page, index, array) => (
                <React.Fragment key={page}>
                  {index > 0 && array[index - 1] !== page - 1 && (
                    <span className="px-2 text-gray-400">...</span>
                  )}
                  <button
                    onClick={() => handlePageChange(page)}
                    className={`w-8 h-8 flex items-center justify-center rounded-md ${
                      currentPage === page
                        ? 'bg-blue-600 text-white'
                        : 'bg-white text-gray-600 hover:bg-gray-50 border border-gray-300'
                    }`}
                  >
                    {page}
                  </button>
                </React.Fragment>
              ))
            }
          </div>

          <button
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            className={`p-2 rounded-md ${
              currentPage === totalPages
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                : 'bg-white text-gray-600 hover:bg-gray-50 border border-gray-300'
            }`}
          >
            <ChevronRight size={20} />
          </button>
        </div>
      )}

      {/* Add/Edit Client Modal */}
      {showAddClientModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <div className="flex justify-between items-center mb-4">
              <h3 className="font-bold text-lg">
                {editingClient ? 'Edit Client' : 'Add New Client'}
              </h3>
              <button
                onClick={() => {
                  setShowAddClientModal(false);
                  setEditingClient(null);
                  setNewClient({
                    name: '',
                    email: '',
                    phone: '',
                    address: '',
                    notes: ''
                  });
                }}
                className="p-1 rounded-full hover:bg-gray-100"
              >
                <X size={20} />
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-gray-700 mb-2 font-medium">
                  <div className="flex items-center">
                    <User size={18} className="mr-2 text-blue-600" />
                    Full Name*
                  </div>
                </label>
                <input
                  type="text"
                  value={newClient.name || ''}
                  onChange={(e) => setNewClient({ ...newClient, name: e.target.value })}
                  className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="John Doe"
                />
              </div>

              <div>
                <label className="block text-gray-700 mb-2 font-medium">
                  <div className="flex items-center">
                    <Mail size={18} className="mr-2 text-blue-600" />
                    Email Address*
                  </div>
                </label>
                <input
                  type="email"
                  value={newClient.email || ''}
                  onChange={(e) => setNewClient({ ...newClient, email: e.target.value })}
                  className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="<EMAIL>"
                />
              </div>

              <div>
                <label className="block text-gray-700 mb-2 font-medium">
                  <div className="flex items-center">
                    <Phone size={18} className="mr-2 text-blue-600" />
                    Phone Number*
                  </div>
                </label>
                <input
                  type="tel"
                  value={newClient.phone || ''}
                  onChange={(e) => setNewClient({ ...newClient, phone: e.target.value })}
                  className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="(*************"
                />
              </div>

              <div>
                <label className="block text-gray-700 mb-2 font-medium">
                  <div className="flex items-center">
                    <MapPin size={18} className="mr-2 text-blue-600" />
                    Address
                  </div>
                </label>
                <textarea
                  value={newClient.address || ''}
                  onChange={(e) => setNewClient({ ...newClient, address: e.target.value })}
                  rows={2}
                  className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="123 Main St, Anytown, ST 12345"
                />
              </div>

              <div>
                <label className="block text-gray-700 mb-2 font-medium">
                  <div className="flex items-center">
                    <FileText size={18} className="mr-2 text-blue-600" />
                    Notes
                  </div>
                </label>
                <textarea
                  value={newClient.notes || ''}
                  onChange={(e) => setNewClient({ ...newClient, notes: e.target.value })}
                  rows={3}
                  className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Additional notes about this client..."
                />
              </div>
            </div>

            <div className="flex justify-end mt-6">
              <button
                onClick={() => {
                  setShowAddClientModal(false);
                  setEditingClient(null);
                  setNewClient({
                    name: '',
                    email: '',
                    phone: '',
                    address: '',
                    notes: ''
                  });
                }}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 mr-2"
              >
                Cancel
              </button>
              <button
                onClick={editingClient ? handleUpdateClient : handleAddClient}
                className="px-4 py-2 bg-blue-600 text-white rounded-md"
              >
                {editingClient ? 'Update Client' : 'Add Client'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Merge Clients Modal */}
      {showMergeModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <div className="flex justify-between items-center mb-4">
              <h3 className="font-bold text-lg">Merge Clients</h3>
              <button
                onClick={() => {
                  setShowMergeModal(false);
                  setClientsToMerge({ primary: null, secondary: null });
                }}
                className="p-1 rounded-full hover:bg-gray-100"
              >
                <X size={20} />
              </button>
            </div>

            <p className="text-gray-600 mb-4">
              Select a primary client (to keep) and a secondary client (to merge into the primary).
              All bookings, activities, and information from the secondary client will be transferred to the primary client.
            </p>

            <div className="space-y-4">
              <div>
                <label className="block text-gray-700 mb-2 font-medium">
                  Primary Client (Keep)
                </label>
                <select
                  value={clientsToMerge.primary || ''}
                  onChange={(e) => setClientsToMerge({ ...clientsToMerge, primary: e.target.value })}
                  className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select Primary Client</option>
                  {clients.map(client => (
                    <option key={`primary-${client.id}`} value={client.id}>
                      {client.name} ({client.email})
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-gray-700 mb-2 font-medium">
                  Secondary Client (Merge)
                </label>
                <select
                  value={clientsToMerge.secondary || ''}
                  onChange={(e) => setClientsToMerge({ ...clientsToMerge, secondary: e.target.value })}
                  className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  disabled={!clientsToMerge.primary}
                >
                  <option value="">Select Secondary Client</option>
                  {clients
                    .filter(client => client.id !== clientsToMerge.primary)
                    .map(client => (
                      <option key={`secondary-${client.id}`} value={client.id}>
                        {client.name} ({client.email})
                      </option>
                    ))
                  }
                </select>
              </div>
            </div>

            <div className="flex justify-end mt-6">
              <button
                onClick={() => {
                  setShowMergeModal(false);
                  setClientsToMerge({ primary: null, secondary: null });
                }}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 mr-2"
              >
                Cancel
              </button>
              <button
                onClick={handleMergeClients}
                disabled={!clientsToMerge.primary || !clientsToMerge.secondary}
                className={`px-4 py-2 rounded-md ${
                  clientsToMerge.primary && clientsToMerge.secondary
                    ? 'bg-blue-600 text-white hover:bg-blue-700'
                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }`}
              >
                Merge Clients
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ClientManagement;
