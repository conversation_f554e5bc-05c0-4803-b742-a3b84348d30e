import React, { useState, useEffect, useRef } from 'react';
import { User, Mail, Phone, MapPin, ArrowLeft, Search } from 'lucide-react';
import { getClients } from '../services/firebase';
import { Client } from '../types';

interface ContactFormProps {
  contactInfo: {
    name: string;
    email: string;
    phone: string;
    address: string;
  };
  onContactSubmit: (contactInfo: {
    name: string;
    email: string;
    phone: string;
    address: string;
  }) => void;
  onBack: () => void;
}

const ContactForm: React.FC<ContactFormProps> = ({ contactInfo, onContactSubmit, onBack }) => {
  const [formData, setFormData] = useState({
    ...contactInfo,
    countryCode: '+973',
    phoneNumber: contactInfo.phone.startsWith('+973') 
      ? contactInfo.phone.substring(4) 
      : contactInfo.phone
  });
  
  const [errors, setErrors] = useState({
    name: '',
    email: '',
    phone: '',
    address: ''
  });
  
  const [clients, setClients] = useState<Client[]>([]);
  const [filteredClients, setFilteredClients] = useState<Client[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const suggestionsRef = useRef<HTMLDivElement>(null);
  
  // Fetch clients from database
  useEffect(() => {
    const loadClients = async () => {
      try {
        const clientsData = await getClients();
        setClients(clientsData);
      } catch (error) {
        console.error('Error loading clients:', error);
      }
    };
    
    loadClients();
  }, []);
  
  // Handle outside click to close suggestions
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (suggestionsRef.current && !suggestionsRef.current.contains(event.target as Node)) {
        setShowSuggestions(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    
    setFormData({
      ...formData,
      [name]: value
    });
    
    // Filter clients based on name input
    if (name === 'name') {
      const filtered = clients.filter(client => 
        client.name.toLowerCase().includes(value.toLowerCase())
      );
      setFilteredClients(filtered);
      setShowSuggestions(value.length > 0 && filtered.length > 0);
    }
  };
  
  const handleSelectClient = (client: Client) => {
    setFormData({
      ...formData,
      name: client.name,
      email: client.email || '',
      countryCode: '+973',
      phoneNumber: (client.phone || '').startsWith('+973') 
        ? (client.phone || '').substring(4) 
        : client.phone || '',
      address: client.address || ''
    });
    setShowSuggestions(false);
  };

  const validateForm = () => {
    let valid = true;
    const newErrors = {
      name: '',
      email: '',
      phone: '',
      address: ''
    };

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
      valid = false;
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
      valid = false;
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email is invalid';
      valid = false;
    }

    if (!formData.phoneNumber.trim()) {
      newErrors.phone = 'Phone number is required';
      valid = false;
    }

    setErrors(newErrors);
    return valid;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      onContactSubmit({
        name: formData.name,
        email: formData.email,
        phone: `${formData.countryCode}${formData.phoneNumber}`,
        address: formData.address
      });
    }
  };

  // Fixed cart at bottom
  const CartSummary = () => (
    <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg p-4 z-10">
      <div className="max-w-4xl mx-auto flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3">
        <div className="flex items-center">
          <User size={20} className="text-blue-600 mr-2 flex-shrink-0" />
          <div>
            <span className="font-medium">
              Contact Information
            </span>
            {formData.name && (
              <div className="text-blue-600 font-bold">
                {formData.name}
              </div>
            )}
          </div>
        </div>
        <button
          onClick={handleSubmit}
          disabled={!formData.name || !formData.email || !formData.phoneNumber}
          className={`px-4 py-2 rounded-md font-medium w-full sm:w-auto ${
            formData.name && formData.email && formData.phoneNumber
              ? 'bg-blue-600 text-white hover:bg-blue-700'
              : 'bg-gray-300 text-gray-500 cursor-not-allowed'
          } transition-colors`}
        >
          Next
        </button>
      </div>
    </div>
  );

  return (
    <div className="pb-20">
      <div className="flex items-center mb-6">
        <button 
          onClick={onBack}
          className="mr-4 p-2 rounded-full hover:bg-gray-100"
        >
          <ArrowLeft size={20} />
        </button>
        <h2 className="text-2xl font-bold">Contact Information</h2>
      </div>
      
      <form onSubmit={handleSubmit}>
        <div className="space-y-4">
          <div className="relative">
            <label className="block text-gray-700 mb-2 font-medium">
              <div className="flex items-center">
                <User size={18} className="mr-2 text-blue-600" />
                Full Name
              </div>
            </label>
            <div className="relative">
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleChange}
                className={`w-full p-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.name ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="John Doe"
                autoComplete="off"
              />
              <Search size={18} className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            </div>
            {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name}</p>}
            
            {/* Client suggestions dropdown */}
            {showSuggestions && (
              <div 
                ref={suggestionsRef}
                className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto"
              >
                {filteredClients.map(client => (
                  <div
                    key={client.id}
                    className="p-2 hover:bg-blue-50 cursor-pointer border-b border-gray-100 flex items-center"
                    onClick={() => handleSelectClient(client)}
                  >
                    <User size={16} className="mr-2 text-gray-400" />
                    <div>
                      <div className="font-medium">{client.name}</div>
                      <div className="text-sm text-gray-500">{client.email}</div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
          
          <div>
            <label className="block text-gray-700 mb-2 font-medium">
              <div className="flex items-center">
                <Mail size={18} className="mr-2 text-blue-600" />
                Email Address
              </div>
            </label>
            <input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              className={`w-full p-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.email ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="<EMAIL>"
            />
            {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email}</p>}
          </div>
          
          <div>
            <label className="block text-gray-700 mb-2 font-medium">
              <div className="flex items-center">
                <Phone size={18} className="mr-2 text-blue-600" />
                Phone Number
              </div>
            </label>
            <div className="flex space-x-2">
              <div className="w-1/4">
                <input
                  type="text"
                  name="countryCode"
                  value={formData.countryCode}
                  onChange={handleChange}
                  className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="+973"
                />
              </div>
              <div className="flex-1">
                <input
                  type="tel"
                  name="phoneNumber"
                  value={formData.phoneNumber}
                  onChange={handleChange}
                  className={`w-full p-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.phone ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="12345678"
                />
              </div>
            </div>
            {errors.phone && <p className="text-red-500 text-sm mt-1">{errors.phone}</p>}
          </div>
          
          <div>
            <label className="block text-gray-700 mb-2 font-medium">
              <div className="flex items-center">
                <MapPin size={18} className="mr-2 text-blue-600" />
                Delivery Address <span className="text-gray-400 text-sm ml-2">(Optional)</span>
              </div>
            </label>
            <textarea
              name="address"
              value={formData.address}
              onChange={handleChange}
              rows={3}
              className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="123 Main St, Anytown, ST 12345"
            />
          </div>
        </div>
      </form>
      
      {/* Fixed Cart Summary */}
      <CartSummary />
    </div>
  );
};

export default ContactForm;