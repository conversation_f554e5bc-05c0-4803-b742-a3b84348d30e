import { doc, setDoc, updateDoc, deleteDoc, collection, getDocs, query, where } from 'firebase/firestore';
import { createUserWithEmailAndPassword, signInWithEmailAndPassword } from 'firebase/auth';
import { auth, db } from '../config/firebase';
import { User, UserRole } from '../types';

// Create a new user (admin only)
export const createUser = async (
  email: string,
  password: string,
  userData: {
    name: string;
    username: string;
    role: UserRole;
  }
): Promise<User> => {
  try {
    // Check if user with same email already exists in Firestore
    const usersRef = collection(db, 'users');
    const emailQuery = query(usersRef, where('email', '==', email.toLowerCase()));
    const emailSnapshot = await getDocs(emailQuery);

    if (!emailSnapshot.empty) {
      throw new Error('A user with this email already exists');
    }

    // Check if user with same username already exists
    const usernameQuery = query(usersRef, where('username', '==', userData.username));
    const usernameSnapshot = await getDocs(usernameQuery);

    if (!usernameSnapshot.empty) {
      throw new Error('A user with this username already exists');
    }

    // Store current user credentials
    const currentUser = auth.currentUser;
    let currentEmail = '';
    let currentPassword = '';

    if (!currentUser) {
      throw new Error('You must be logged in to create a user');
    }

    try {
      // Create user in Firebase Authentication
      // First, sign out the current user
      await auth.signOut();

      // Create the new user
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const firebaseUser = userCredential.user;

      // Create user document in Firestore
      const user: Omit<User, 'id'> = {
        email: email.toLowerCase(),
        username: userData.username,
        name: userData.name,
        role: userData.role,
        createdAt: new Date().toISOString(),
        isActive: true
      };

      // Use the Firebase Auth UID as the document ID in Firestore
      await setDoc(doc(db, 'users', firebaseUser.uid), user);

      // Sign out the new user
      await auth.signOut();

      // Sign back in as the admin user
      // This would require storing the admin credentials securely
      // For now, we'll redirect to the login page
      window.location.href = '/login';

      // Return the created user
      return {
        id: firebaseUser.uid,
        ...user
      };
    } catch (error: any) {
      console.error('Error creating user:', error);

      // If there was an error, try to sign back in as the admin
      window.location.href = '/login';

      throw error;
    }
  } catch (error: any) {
    console.error('Error creating user:', error);
    throw error;
  }
};

// Update a user (admin only)
export const updateUserProfile = async (
  userId: string,
  userData: Partial<Omit<User, 'id'>>
): Promise<void> => {
  try {
    await updateDoc(doc(db, 'users', userId), userData);
  } catch (error) {
    console.error('Error updating user:', error);
    throw error;
  }
};

// Delete a user (admin only)
export const deleteUserAccount = async (userId: string): Promise<void> => {
  try {
    // For now, we can only delete the user document from Firestore
    // Deleting from Firebase Authentication would require Firebase Admin SDK
    await deleteDoc(doc(db, 'users', userId));
  } catch (error) {
    console.error('Error deleting user:', error);
    throw error;
  }
};
