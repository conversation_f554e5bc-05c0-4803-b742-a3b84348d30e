import { doc, setDoc, getDoc } from 'firebase/firestore';
import { db, auth } from '../config/firebase';
import { User } from '../types';
import { signInWithEmailAndPassword } from 'firebase/auth';

/**
 * Sets up the admin user in Firestore after they've been created in Firebase Authentication
 */
export const setupAdminUser = async (email: string, password: string): Promise<User | null> => {
  try {
    // Sign in with the admin credentials
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    const firebaseUser = userCredential.user;
    
    // Check if the user document already exists
    const userDocRef = doc(db, 'users', firebaseUser.uid);
    const userDoc = await getDoc(userDocRef);
    
    if (!userDoc.exists()) {
      // Create the admin user document in Firestore
      const adminData: Omit<User, 'id'> = {
        email: email.toLowerCase(),
        name: 'Admin',
        username: 'admin',
        role: 'admin',
        createdAt: new Date().toISOString(),
        isActive: true
      };
      
      await setDoc(userDocRef, adminData);
      
      console.log('Admin user document created successfully');
      
      return {
        id: firebaseUser.uid,
        ...adminData
      };
    } else {
      console.log('Admin user document already exists');
      
      return {
        id: firebaseUser.uid,
        ...userDoc.data() as Omit<User, 'id'>
      };
    }
  } catch (error) {
    console.error('Error setting up admin user:', error);
    return null;
  }
};
