import { getMessaging, getToken, onMessage } from 'firebase/messaging';
import { User } from '../types';
import { doc, setDoc, getDoc } from 'firebase/firestore';
import { db, auth } from '../config/firebase';

// Initialize Firebase Cloud Messaging
const messaging = getMessaging();

// Vapid key from Firebase console (Web Push certificates)
const vapidKey = 'REPLACE_WITH_YOUR_VAPID_KEY';

/**
 * Request permission for notifications
 */
export const requestNotificationPermission = async (): Promise<boolean> => {
  try {
    const permission = await Notification.requestPermission();
    return permission === 'granted';
  } catch (error) {
    console.error('Error requesting notification permission:', error);
    return false;
  }
};

/**
 * Register the device for push notifications
 */
export const registerForPushNotifications = async (user: User): Promise<string | null> => {
  try {
    // Check if notification permission is granted
    if (Notification.permission !== 'granted') {
      const permissionGranted = await requestNotificationPermission();
      if (!permissionGranted) {
        console.log('Notification permission denied');
        return null;
      }
    }

    // Get FCM token
    const token = await getToken(messaging, { vapidKey });

    if (token) {
      console.log('FCM Token:', token);

      // Save the token to the user's document in Firestore
      await saveTokenToDatabase(token, user.id);

      return token;
    } else {
      console.log('No registration token available');
      return null;
    }
  } catch (error) {
    console.error('Error registering for push notifications:', error);
    return null;
  }
};

/**
 * Save the FCM token to the user's document in Firestore
 */
const saveTokenToDatabase = async (token: string, userId: string): Promise<void> => {
  try {
    const userRef = doc(db, 'users', userId);
    const userDoc = await getDoc(userRef);

    if (userDoc.exists()) {
      const userData = userDoc.data();

      // Update the user document with the FCM token
      await setDoc(userRef, {
        ...userData,
        fcmTokens: userData.fcmTokens ? [...userData.fcmTokens, token] : [token]
      }, { merge: true });

      console.log('Token saved to database');
    }
  } catch (error) {
    console.error('Error saving token to database:', error);
  }
};

/**
 * Handle foreground messages
 */
export const setupForegroundNotifications = (): void => {
  onMessage(messaging, (payload) => {
    console.log('Foreground message received:', payload);

    // Display a notification
    if (payload.notification) {
      const { title, body } = payload.notification;

      // Show notification
      if (Notification.permission === 'granted' && title) {
        new Notification(title, {
          body: body || '',
          icon: '/logo192.png'
        });
      }
    }
  });
};

/**
 * Send a notification to a specific user
 */
export const sendNotificationToUser = async (
  userId: string,
  title: string,
  body: string,
  data?: Record<string, string>
): Promise<void> => {
  try {
    // In a real implementation, you would call a Cloud Function or backend API
    // to send the notification using the FCM Admin SDK
    console.log(`Sending notification to user ${userId}: ${title} - ${body}`);

    // This is a placeholder for the actual implementation
    // You'll need to implement a Cloud Function for this
  } catch (error) {
    console.error('Error sending notification:', error);
  }
};

/**
 * Send a notification to all admin users
 */
export const sendNotificationToAdmins = async (
  title: string,
  body: string,
  data?: Record<string, string>
): Promise<void> => {
  try {
    // In a real implementation, you would call a Cloud Function or backend API
    // to send the notification to all admin users
    console.log(`Sending notification to all admins: ${title} - ${body}`);

    // This is a placeholder for the actual implementation
    // You'll need to implement a Cloud Function for this
  } catch (error) {
    console.error('Error sending notification to admins:', error);
  }
};
