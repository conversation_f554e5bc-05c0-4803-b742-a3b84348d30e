import { addUser, getUser<PERSON>yEmail, getUserByUsername } from './firebase';
import { User } from '../types';

export const seedDefaultAdmin = async (): Promise<User | null> => {
  try {
    // Check if admin already exists by email or username
    const existingAdminByEmail = await getUserByEmail('<EMAIL>');
    const existingAdminByUsername = await getUserByUsername('admin');
    
    if (existingAdminByEmail) {
      console.log('Default admin user already exists (by email)');
      
      // Update username if it doesn't exist
      if (!existingAdminByEmail.username) {
        const updatedAdmin = {
          ...existingAdminByEmail,
          username: 'admin'
        };
        
        await import('./firebase').then(({ updateUser }) => {
          updateUser(updatedAdmin);
        });
        
        console.log('Updated existing admin with username');
        return updatedAdmin;
      }
      
      return existingAdminByEmail;
    }
    
    if (existingAdminByUsername) {
      console.log('Default admin user already exists (by username)');
      return existingAdminByUsername;
    }
    
    // Create default admin user
    const defaultAdmin = {
      name: 'Admin',
      email: '<EMAIL>',
      username: 'admin',
      password: 'admin123', // In a real app, this would be hashed
      role: 'admin' as const,
      createdAt: new Date().toISOString(),
      isActive: true
    };
    
    const adminId = await addUser(defaultAdmin);
    const createdAdmin: User = {
      id: adminId,
      name: defaultAdmin.name,
      email: defaultAdmin.email,
      username: defaultAdmin.username,
      role: defaultAdmin.role,
      createdAt: defaultAdmin.createdAt,
      isActive: defaultAdmin.isActive
    };
    
    console.log('Default admin user created successfully');
    return createdAdmin;
  } catch (error) {
    console.error('Error seeding default admin user:', error);
    return null;
  }
}; 