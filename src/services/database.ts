import { executeQuery, getOne, getMany, insertRecord, updateRecord, deleteRecord } from '../config/database';
import { Product, Booking, Coupon, Client, ClientActivity, SystemSettings, User, UserRole, Vendor, VendorTransaction, Payment, DeliveryOption } from '../types';
import { sendNewBookingNotification, sendAdminDirectNotification } from './oneSignalService';

// Generate unique ID helper
const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

// Products
export const getProducts = async (): Promise<Product[]> => {
  const query = 'SELECT * FROM products ORDER BY name';
  const results = await getMany(query);
  return results.map(row => ({
    id: row.id,
    name: row.name,
    category: row.category,
    sku: row.sku,
    barcode: row.barcode,
    dailyRate: parseFloat(row.daily_rate),
    weeklyRate: row.weekly_rate ? parseFloat(row.weekly_rate) : undefined,
    image: row.image,
    description: row.description,
    available: Boolean(row.available),
    quantity: row.quantity,
    stock: row.stock,
    featured: Boolean(row.featured),
    customDays: row.custom_days,
    customPrice: row.custom_price ? parseFloat(row.custom_price) : undefined,
    temporaryDailyRate: row.temporary_daily_rate ? parseFloat(row.temporary_daily_rate) : undefined,
    temporaryWeeklyRate: row.temporary_weekly_rate ? parseFloat(row.temporary_weekly_rate) : undefined,
    isExternalVendorItem: Boolean(row.is_external_vendor_item),
    vendorId: row.vendor_id,
    vendorSku: row.vendor_sku,
    vendorCost: row.vendor_cost ? parseFloat(row.vendor_cost) : undefined,
    profitMargin: row.profit_margin ? parseFloat(row.profit_margin) : undefined
  }));
};

export const addProduct = async (product: Omit<Product, 'id'>): Promise<string> => {
  const id = generateId();
  const query = `
    INSERT INTO products (
      id, name, category, sku, barcode, daily_rate, weekly_rate, image, description,
      available, quantity, stock, featured, custom_days, custom_price,
      temporary_daily_rate, temporary_weekly_rate, is_external_vendor_item,
      vendor_id, vendor_sku, vendor_cost, profit_margin
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `;
  
  await executeQuery(query, [
    id, product.name, product.category, product.sku, product.barcode,
    product.dailyRate, product.weeklyRate, product.image, product.description,
    product.available, product.quantity, product.stock, product.featured,
    product.customDays, product.customPrice, product.temporaryDailyRate,
    product.temporaryWeeklyRate, product.isExternalVendorItem, product.vendorId,
    product.vendorSku, product.vendorCost, product.profitMargin
  ]);
  
  return id;
};

export const updateProduct = async (product: Product): Promise<void> => {
  const query = `
    UPDATE products SET
      name = ?, category = ?, sku = ?, barcode = ?, daily_rate = ?, weekly_rate = ?,
      image = ?, description = ?, available = ?, quantity = ?, stock = ?, featured = ?,
      custom_days = ?, custom_price = ?, temporary_daily_rate = ?, temporary_weekly_rate = ?,
      is_external_vendor_item = ?, vendor_id = ?, vendor_sku = ?, vendor_cost = ?, profit_margin = ?
    WHERE id = ?
  `;
  
  await executeQuery(query, [
    product.name, product.category, product.sku, product.barcode, product.dailyRate,
    product.weeklyRate, product.image, product.description, product.available,
    product.quantity, product.stock, product.featured, product.customDays,
    product.customPrice, product.temporaryDailyRate, product.temporaryWeeklyRate,
    product.isExternalVendorItem, product.vendorId, product.vendorSku,
    product.vendorCost, product.profitMargin, product.id
  ]);
};

export const deleteProduct = async (id: string): Promise<void> => {
  await executeQuery('DELETE FROM products WHERE id = ?', [id]);
};

// Categories
export const getCategories = async (): Promise<string[]> => {
  const query = 'SELECT DISTINCT category FROM products ORDER BY category';
  const results = await getMany(query);
  return results.map(row => row.category);
};

// Bookings
export const getBookings = async (): Promise<Booking[]> => {
  const query = `
    SELECT b.*, 
           GROUP_CONCAT(
             CONCAT(bp.product_id, ':', bp.quantity, ':', bp.daily_rate, ':', bp.weekly_rate, ':', bp.total_price)
             SEPARATOR '|'
           ) as products_data
    FROM bookings b
    LEFT JOIN booking_products bp ON b.id = bp.booking_id
    GROUP BY b.id
    ORDER BY b.date DESC
  `;
  
  const results = await getMany(query);
  
  return results.map(row => ({
    id: row.id,
    quoteNumber: row.quote_number,
    date: row.date.toISOString(),
    customer: {
      name: row.customer_name,
      email: row.customer_email,
      phone: row.customer_phone,
      address: row.customer_address
    },
    products: row.products_data ? row.products_data.split('|').map((productData: string) => {
      const [productId, quantity, dailyRate, weeklyRate, totalPrice] = productData.split(':');
      return {
        id: productId,
        quantity: parseInt(quantity),
        dailyRate: parseFloat(dailyRate),
        weeklyRate: weeklyRate !== 'null' ? parseFloat(weeklyRate) : undefined,
        totalPrice: parseFloat(totalPrice)
      };
    }) : [],
    rentalPeriod: {
      dates: JSON.parse(row.rental_dates),
      days: row.rental_days,
      rentalType: row.rental_type
    },
    status: row.status,
    subtotal: parseFloat(row.subtotal),
    deliveryFee: parseFloat(row.delivery_fee),
    discount: parseFloat(row.discount),
    tax: parseFloat(row.tax),
    total: parseFloat(row.total),
    paidAmount: row.paid_amount ? parseFloat(row.paid_amount) : 0,
    remainingAmount: row.remaining_amount ? parseFloat(row.remaining_amount) : 0
  }));
};

export const addBooking = async (booking: Omit<Booking, 'id'>): Promise<string> => {
  const id = booking.quoteNumber || generateId();
  
  // Insert booking
  const bookingQuery = `
    INSERT INTO bookings (
      id, quote_number, date, customer_name, customer_email, customer_phone, customer_address,
      rental_dates, rental_days, rental_type, status, subtotal, delivery_fee, discount,
      tax, total, paid_amount, remaining_amount, delivery_option_id, coupon_id
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `;
  
  await executeQuery(bookingQuery, [
    id, booking.quoteNumber, new Date(booking.date), booking.customer.name,
    booking.customer.email, booking.customer.phone, booking.customer.address,
    JSON.stringify(booking.rentalPeriod.dates), booking.rentalPeriod.days,
    booking.rentalPeriod.rentalType, booking.status, booking.subtotal,
    booking.deliveryFee, booking.discount, booking.tax, booking.total,
    booking.paidAmount || 0, booking.remainingAmount || 0,
    booking.delivery?.option?.id, booking.coupon?.id
  ]);
  
  // Insert booking products
  for (const product of booking.products) {
    const productQuery = `
      INSERT INTO booking_products (booking_id, product_id, quantity, daily_rate, weekly_rate, total_price)
      VALUES (?, ?, ?, ?, ?, ?)
    `;
    await executeQuery(productQuery, [
      id, product.id, product.quantity, product.dailyRate, product.weeklyRate, product.totalPrice
    ]);
  }
  
  // Send notification (using existing OneSignal implementation)
  try {
    await sendNewBookingNotification({
      customerName: booking.customer.name,
      date: booking.date,
      quoteNumber: booking.quoteNumber
    });
  } catch (error) {
    console.error('Failed to send booking notification:', error);
  }
  
  return id;
};

export const updateBooking = async (booking: Booking): Promise<void> => {
  // Update booking
  const bookingQuery = `
    UPDATE bookings SET
      quote_number = ?, date = ?, customer_name = ?, customer_email = ?, customer_phone = ?,
      customer_address = ?, rental_dates = ?, rental_days = ?, rental_type = ?, status = ?,
      subtotal = ?, delivery_fee = ?, discount = ?, tax = ?, total = ?, paid_amount = ?,
      remaining_amount = ?, delivery_option_id = ?, coupon_id = ?
    WHERE id = ?
  `;
  
  await executeQuery(bookingQuery, [
    booking.quoteNumber, new Date(booking.date), booking.customer.name,
    booking.customer.email, booking.customer.phone, booking.customer.address,
    JSON.stringify(booking.rentalPeriod.dates), booking.rentalPeriod.days,
    booking.rentalPeriod.rentalType, booking.status, booking.subtotal,
    booking.deliveryFee, booking.discount, booking.tax, booking.total,
    booking.paidAmount || 0, booking.remainingAmount || 0,
    booking.delivery?.option?.id, booking.coupon?.id, booking.id
  ]);
  
  // Delete existing booking products
  await executeQuery('DELETE FROM booking_products WHERE booking_id = ?', [booking.id]);
  
  // Insert updated booking products
  for (const product of booking.products) {
    const productQuery = `
      INSERT INTO booking_products (booking_id, product_id, quantity, daily_rate, weekly_rate, total_price)
      VALUES (?, ?, ?, ?, ?, ?)
    `;
    await executeQuery(productQuery, [
      booking.id, product.id, product.quantity, product.dailyRate, product.weeklyRate, product.totalPrice
    ]);
  }
};

export const deleteBooking = async (id: string): Promise<void> => {
  // Booking products will be deleted automatically due to CASCADE
  await executeQuery('DELETE FROM bookings WHERE id = ?', [id]);
};

// Coupons
export const getCoupons = async (): Promise<Coupon[]> => {
  const query = 'SELECT * FROM coupons ORDER BY code';
  const results = await getMany(query);
  return results.map(row => ({
    id: row.id,
    code: row.code,
    discountType: row.discount_type,
    discountValue: parseFloat(row.discount_value),
    expiryDate: row.expiry_date ? row.expiry_date.toISOString().split('T')[0] : '',
    active: Boolean(row.active)
  }));
};

export const addCoupon = async (coupon: Omit<Coupon, 'id'>): Promise<string> => {
  const id = generateId();
  const query = `
    INSERT INTO coupons (id, code, discount_type, discount_value, expiry_date, active)
    VALUES (?, ?, ?, ?, ?, ?)
  `;

  await executeQuery(query, [
    id, coupon.code, coupon.discountType, coupon.discountValue,
    coupon.expiryDate ? new Date(coupon.expiryDate) : null, coupon.active
  ]);

  return id;
};

export const updateCoupon = async (coupon: Coupon): Promise<void> => {
  const query = `
    UPDATE coupons SET code = ?, discount_type = ?, discount_value = ?, expiry_date = ?, active = ?
    WHERE id = ?
  `;

  await executeQuery(query, [
    coupon.code, coupon.discountType, coupon.discountValue,
    coupon.expiryDate ? new Date(coupon.expiryDate) : null, coupon.active, coupon.id
  ]);
};

export const deleteCoupon = async (id: string): Promise<void> => {
  await executeQuery('DELETE FROM coupons WHERE id = ?', [id]);
};

// Clients
export const getClients = async (): Promise<Client[]> => {
  const query = 'SELECT * FROM clients ORDER BY name';
  const results = await getMany(query);
  return results.map(row => ({
    id: row.id,
    name: row.name,
    email: row.email,
    phone: row.phone,
    address: row.address,
    dateAdded: row.date_added.toISOString(),
    totalBookings: row.total_bookings,
    totalSpent: parseFloat(row.total_spent),
    pendingPayment: row.pending_payment ? parseFloat(row.pending_payment) : 0,
    lastBooking: row.last_booking ? row.last_booking.toISOString() : undefined,
    notes: row.notes
  }));
};

export const addClient = async (client: Omit<Client, 'id'>): Promise<string> => {
  const normalizedEmail = client.email.toLowerCase().trim();

  // Check if client already exists
  const existingClient = await getOne('SELECT id FROM clients WHERE email = ?', [normalizedEmail]);
  if (existingClient) {
    throw new Error('A client with this email already exists');
  }

  const query = `
    INSERT INTO clients (id, name, email, phone, address, total_bookings, total_spent, pending_payment, notes)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
  `;

  await executeQuery(query, [
    normalizedEmail, client.name, normalizedEmail, client.phone, client.address,
    client.totalBookings || 0, client.totalSpent || 0, client.pendingPayment || 0, client.notes
  ]);

  return normalizedEmail;
};

export const updateClient = async (client: Client): Promise<void> => {
  const query = `
    UPDATE clients SET name = ?, phone = ?, address = ?, total_bookings = ?,
                      total_spent = ?, pending_payment = ?, last_booking = ?, notes = ?
    WHERE id = ?
  `;

  await executeQuery(query, [
    client.name, client.phone, client.address, client.totalBookings,
    client.totalSpent, client.pendingPayment,
    client.lastBooking ? new Date(client.lastBooking) : null, client.notes, client.id
  ]);
};

export const deleteClient = async (email: string): Promise<void> => {
  const normalizedEmail = email.toLowerCase().trim();
  const client = await getOne('SELECT id FROM clients WHERE email = ?', [normalizedEmail]);

  if (!client) {
    throw new Error('Client not found');
  }

  await executeQuery('DELETE FROM clients WHERE email = ?', [normalizedEmail]);
};

// Client Activities
export const getClientActivities = async (): Promise<ClientActivity[]> => {
  const query = 'SELECT * FROM client_activities ORDER BY date DESC';
  const results = await getMany(query);
  return results.map(row => ({
    id: row.id,
    clientId: row.client_id,
    date: row.date.toISOString(),
    type: row.type,
    description: row.description,
    amount: row.amount ? parseFloat(row.amount) : undefined,
    bookingId: row.booking_id,
    quoteNumber: row.quote_number
  }));
};

export const addClientActivity = async (activity: Omit<ClientActivity, 'id'>): Promise<string> => {
  const id = generateId();
  const query = `
    INSERT INTO client_activities (id, client_id, date, type, description, amount, booking_id, quote_number)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
  `;

  await executeQuery(query, [
    id, activity.clientId, new Date(activity.date), activity.type,
    activity.description, activity.amount, activity.bookingId, activity.quoteNumber
  ]);

  return id;
};

// System Settings
export const getSystemSettings = async (): Promise<SystemSettings> => {
  const result = await getOne('SELECT * FROM system_settings WHERE id = ?', ['general']);

  if (!result) {
    // Return default settings if none exist
    const defaultSettings: SystemSettings = {
      taxRate: 0,
      enableTax: false,
      deliveryOptions: [],
      lastQuoteNumber: 0
    };

    // Insert default settings
    await executeQuery(
      'INSERT INTO system_settings (id, tax_rate, enable_tax, last_quote_number, settings_data) VALUES (?, ?, ?, ?, ?)',
      ['general', 0, false, 0, JSON.stringify(defaultSettings)]
    );

    return defaultSettings;
  }

  const settingsData = result.settings_data ? JSON.parse(result.settings_data) : {};

  return {
    taxRate: parseFloat(result.tax_rate) || 0,
    enableTax: Boolean(result.enable_tax),
    lastQuoteNumber: result.last_quote_number || 0,
    deliveryOptions: settingsData.deliveryOptions || []
  };
};

export const updateSystemSettings = async (settings: SystemSettings): Promise<void> => {
  const query = `
    INSERT INTO system_settings (id, tax_rate, enable_tax, last_quote_number, settings_data)
    VALUES (?, ?, ?, ?, ?)
    ON DUPLICATE KEY UPDATE
    tax_rate = VALUES(tax_rate),
    enable_tax = VALUES(enable_tax),
    last_quote_number = VALUES(last_quote_number),
    settings_data = VALUES(settings_data)
  `;

  await executeQuery(query, [
    'general', settings.taxRate, settings.enableTax, settings.lastQuoteNumber,
    JSON.stringify(settings)
  ]);
};

// Users
export const getUsers = async (): Promise<User[]> => {
  const query = 'SELECT * FROM users WHERE is_active = true ORDER BY name';
  const results = await getMany(query);
  return results.map(row => ({
    id: row.id,
    email: row.email,
    username: row.username,
    name: row.name,
    role: row.role,
    createdAt: row.created_at.toISOString(),
    lastLogin: row.last_login ? row.last_login.toISOString() : undefined,
    isActive: Boolean(row.is_active)
  }));
};

export const addUser = async (user: Omit<User, 'id'>): Promise<string> => {
  // Check for existing email
  const existingEmail = await getOne('SELECT id FROM users WHERE email = ?', [user.email.toLowerCase()]);
  if (existingEmail) {
    throw new Error('A user with this email already exists');
  }

  // Check for existing username
  const existingUsername = await getOne('SELECT id FROM users WHERE username = ?', [user.username]);
  if (existingUsername) {
    throw new Error('A user with this username already exists');
  }

  const id = generateId();
  const query = `
    INSERT INTO users (id, email, username, name, password, role, is_active)
    VALUES (?, ?, ?, ?, ?, ?, ?)
  `;

  // Note: In production, you should hash the password
  await executeQuery(query, [
    id, user.email.toLowerCase(), user.username, user.name,
    'defaultpassword', user.role, true
  ]);

  return id;
};

export const authenticateUser = async (username: string, password: string): Promise<User | null> => {
  // Try username first
  let user = await getOne(
    'SELECT * FROM users WHERE username = ? AND password = ? AND is_active = true',
    [username, password]
  );

  // If not found, try email
  if (!user) {
    user = await getOne(
      'SELECT * FROM users WHERE email = ? AND password = ? AND is_active = true',
      [username.toLowerCase(), password]
    );
  }

  if (user) {
    // Update last login
    await executeQuery('UPDATE users SET last_login = NOW() WHERE id = ?', [user.id]);

    return {
      id: user.id,
      email: user.email,
      username: user.username,
      name: user.name,
      role: user.role,
      createdAt: user.created_at.toISOString(),
      lastLogin: new Date().toISOString(),
      isActive: Boolean(user.is_active)
    };
  }

  return null;
};

// Vendors
export const getVendors = async (): Promise<Vendor[]> => {
  const query = 'SELECT * FROM vendors ORDER BY name';
  const results = await getMany(query);
  return results.map(row => ({
    id: row.id,
    name: row.name,
    contactName: row.contact_name,
    email: row.email,
    phone: row.phone,
    address: row.address,
    notes: row.notes,
    paymentTerms: row.payment_terms,
    active: Boolean(row.active),
    taxPercentage: row.tax_percentage ? parseFloat(row.tax_percentage) : 0,
    createdAt: row.created_at.toISOString()
  }));
};

export const addVendor = async (vendor: Omit<Vendor, 'id'>): Promise<string> => {
  const id = generateId();
  const query = `
    INSERT INTO vendors (id, name, contact_name, email, phone, address, notes, payment_terms, active, tax_percentage)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `;

  await executeQuery(query, [
    id, vendor.name, vendor.contactName, vendor.email, vendor.phone,
    vendor.address, vendor.notes, vendor.paymentTerms, vendor.active, vendor.taxPercentage
  ]);

  return id;
};
