import { Product, Booking, Coupon, Client, ClientActivity, SystemSettings, User, UserRole, Vendor, VendorTransaction, Payment, DeliveryOption } from '../types';
import { sendNewBookingNotification, sendAdminDirectNotification } from './oneSignalService';

const API_BASE_URL = 'http://localhost:3001/api';

// API helper function
const apiCall = async (endpoint: string, options: RequestInit = {}) => {
  const response = await fetch(`${API_BASE_URL}${endpoint}`, {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  });

  if (!response.ok) {
    const error = await response.json().catch(() => ({ error: 'Unknown error' }));
    throw new Error(error.error || `HTTP ${response.status}`);
  }

  return response.json();
};

// Generate unique ID helper
const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

// Products
export const getProducts = async (): Promise<Product[]> => {
  return apiCall('/products');
};

export const addProduct = async (product: Omit<Product, 'id'>): Promise<string> => {
  const result = await apiCall('/products', {
    method: 'POST',
    body: JSON.stringify(product),
  });
  return result.id;
};

export const updateProduct = async (product: Product): Promise<void> => {
  console.log('updateProduct not yet implemented');
};

export const deleteProduct = async (id: string): Promise<void> => {
  console.log('deleteProduct not yet implemented');
};

// Categories
export const getCategories = async (): Promise<string[]> => {
  return apiCall('/categories');
};

// Bookings
export const getBookings = async (): Promise<Booking[]> => {
  return apiCall('/bookings');
};

export const addBooking = async (booking: Omit<Booking, 'id'>): Promise<string> => {
  const result = await apiCall('/bookings', {
    method: 'POST',
    body: JSON.stringify(booking),
  });
  
  // Send notification (using existing OneSignal implementation)
  try {
    await sendNewBookingNotification({
      customerName: booking.customer.name,
      date: booking.date,
      quoteNumber: booking.quoteNumber
    });
  } catch (error) {
    console.error('Failed to send booking notification:', error);
  }
  
  return result.id;
};

export const updateBooking = async (booking: Booking): Promise<void> => {
  console.log('updateBooking not yet implemented');
};

export const deleteBooking = async (id: string): Promise<void> => {
  console.log('deleteBooking not yet implemented');
};

// Coupons
export const getCoupons = async (): Promise<Coupon[]> => {
  return [];
};

export const addCoupon = async (coupon: Omit<Coupon, 'id'>): Promise<string> => {
  return generateId();
};

export const updateCoupon = async (coupon: Coupon): Promise<void> => {
  console.log('updateCoupon not yet implemented');
};

export const deleteCoupon = async (id: string): Promise<void> => {
  console.log('deleteCoupon not yet implemented');
};

// Clients
export const getClients = async (): Promise<Client[]> => {
  return [];
};

export const addClient = async (client: Omit<Client, 'id'>): Promise<string> => {
  return generateId();
};

export const updateClient = async (client: Client): Promise<void> => {
  console.log('updateClient not yet implemented');
};

export const deleteClient = async (email: string): Promise<void> => {
  console.log('deleteClient not yet implemented');
};

// Client Activities
export const getClientActivities = async (): Promise<ClientActivity[]> => {
  return [];
};

export const addClientActivity = async (activity: Omit<ClientActivity, 'id'>): Promise<string> => {
  return generateId();
};

// System Settings
export const getSystemSettings = async (): Promise<SystemSettings> => {
  return apiCall('/system-settings');
};

export const updateSystemSettings = async (settings: SystemSettings): Promise<void> => {
  console.log('updateSystemSettings not yet implemented');
};

// Users
export const getUsers = async (): Promise<User[]> => {
  return [];
};

export const addUser = async (user: Omit<User, 'id'>): Promise<string> => {
  return generateId();
};

export const authenticateUser = async (username: string, password: string): Promise<User | null> => {
  try {
    const user = await apiCall('/auth/login', {
      method: 'POST',
      body: JSON.stringify({ username, password }),
    });
    return user;
  } catch (error) {
    console.error('Authentication failed:', error);
    return null;
  }
};

// Vendors
export const getVendors = async (): Promise<Vendor[]> => {
  return [];
};

export const addVendor = async (vendor: Omit<Vendor, 'id'>): Promise<string> => {
  return generateId();
};
