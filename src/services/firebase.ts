import {
  collection,
  doc,
  getDocs,
  getDoc,
  setDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  addDoc,
  Timestamp,
  DocumentData,
  writeBatch,
  serverTimestamp
} from 'firebase/firestore';
import { db } from '../config/firebase';
import { Product, Booking, Coupon, Client, ClientActivity, SystemSettings, User, UserRole, Vendor, VendorTransaction } from '../types';
import { systemSettings as initialSystemSettings } from '../data/systemSettings';
import { sendNewBookingNotification, sendAdminDirectNotification } from './oneSignalService';

// Add TypeScript definitions for OneSignal
declare global {
  interface Window {
    OneSignal: any;
  }
}

// Products
export const getProducts = async (): Promise<Product[]> => {
  const querySnapshot = await getDocs(collection(db, 'products'));
  return querySnapshot.docs.map(doc => ({
    id: String(doc.id),
    ...doc.data()
  } as unknown as Product));
};

export const updateProduct = async (product: Product): Promise<void> => {
  try {
    if (!product.id) {
      throw new Error('Product ID is required');
    }

    const productRef = doc(db, 'products', product.id);
    const { id, ...productData } = product;
    await updateDoc(productRef, productData);
  } catch (error) {
    console.error('Error updating product:', error);
    throw error;
  }
};

export const addProduct = async (product: Omit<Product, 'id'>): Promise<string> => {
  try {
    // Validate required fields
    if (!product.name || !product.sku) {
      throw new Error('Product name and SKU are required');
    }

    // Check if product with same SKU already exists
    const productsRef = collection(db, 'products');
    const q = query(productsRef, where('sku', '==', product.sku));
    const querySnapshot = await getDocs(q);

    if (!querySnapshot.empty) {
      throw new Error('A product with this SKU already exists');
    }

    // If no duplicate found, add the new product
    const docRef = await addDoc(productsRef, {
      ...product,
      createdAt: Timestamp.now(),
      // Ensure all required fields have default values
      dailyRate: product.dailyRate || 0,
      weeklyRate: product.weeklyRate || 0,
      quantity: product.quantity || 0,
      category: product.category || 'Uncategorized',
      image: product.image || '',
      description: product.description || ''
    });
    return docRef.id;
  } catch (error) {
    console.error('Error adding product:', error);
    throw error;
  }
};

export const deleteProduct = async (productId: string): Promise<void> => {
  try {
    if (!productId) {
      throw new Error('Product ID is required');
    }

    // First check if the product exists
    const productRef = doc(db, 'products', productId);
    const productDoc = await getDoc(productRef);

    if (!productDoc.exists()) {
      console.error('Product not found:', productId);
      throw new Error(`Product with ID ${productId} not found`);
    }

    // Delete the product
    await deleteDoc(productRef);
    console.log('Product deleted successfully:', productId);
  } catch (error) {
    console.error('Error deleting product:', error);
    throw error;
  }
};

// Bookings
export const getBookings = async (): Promise<Booking[]> => {
  const querySnapshot = await getDocs(collection(db, 'bookings'));
  return querySnapshot.docs.map(doc => {
    const data = doc.data();
    return {
      id: doc.id,
      ...data,
      date: data.date?.toDate?.()?.toISOString() || new Date().toISOString(),
      rentalPeriod: {
        ...data.rentalPeriod,
        dates: Array.isArray(data.rentalPeriod?.dates)
          ? data.rentalPeriod.dates.map((date: string) => new Date(date).toISOString())
          : [],
        days: data.rentalPeriod?.days || 0,
        rentalType: data.rentalPeriod?.rentalType || 'daily'
      }
    } as Booking;
  });
};

export const getBookingByQuoteNumber = async (quoteNumber: string): Promise<Booking | null> => {
  try {
    const bookingRef = doc(db, 'bookings', quoteNumber);
    const docSnap = await getDoc(bookingRef);

    if (!docSnap.exists()) {
      return null;
    }

    const data = docSnap.data();
    return {
      id: docSnap.id,
      ...data,
      date: data.date?.toDate?.()?.toISOString() || new Date().toISOString(),
      rentalPeriod: {
        ...data.rentalPeriod,
        dates: Array.isArray(data.rentalPeriod?.dates)
          ? data.rentalPeriod.dates.map((date: string) => new Date(date).toISOString())
          : [],
        days: data.rentalPeriod?.days || 0,
        rentalType: data.rentalPeriod?.rentalType || 'daily'
      }
    } as Booking;
  } catch (error) {
    console.error('Error getting booking by quote number:', error);
    return null;
  }
};

import { toBookingProduct } from '../types/bookingTypes';

export const addBooking = async (booking: Omit<Booking, 'id'>): Promise<string> => {
  try {
    // Ensure all dates are valid and simplify product data
    const bookingData = {
      ...booking,
      date: new Date().toISOString(),
      // Simplify product data to reduce database read/write operations
      products: booking.products.map(product => toBookingProduct(product)),
      rentalPeriod: {
        ...booking.rentalPeriod,
        dates: Array.isArray(booking.rentalPeriod.dates)
          ? booking.rentalPeriod.dates.map(date => new Date(date).toISOString())
          : []
      }
    };

    // Remove any undefined values that would cause Firebase errors
    const cleanBookingData = JSON.parse(JSON.stringify(bookingData));

    let bookingId: string;

    // Use the quoteNumber as the document ID instead of letting Firebase generate one
    // Make sure the quoteNumber exists and is a non-empty string
    if (!booking.quoteNumber || booking.quoteNumber.trim() === '') {
      // If no quote number, generate a document with auto-ID
      const docRef = await addDoc(collection(db, 'bookings'), {
        ...cleanBookingData,
        date: Timestamp.fromDate(new Date(bookingData.date))
      });
      bookingId = docRef.id;
    } else {
      // Use the quoteNumber as the document ID
      const bookingRef = doc(db, 'bookings', booking.quoteNumber);
      await setDoc(bookingRef, {
        ...cleanBookingData,
        date: Timestamp.fromDate(new Date(bookingData.date))
      });

      bookingId = booking.quoteNumber;
    }

    // Send notification for new booking
    try {
      console.log('Booking created successfully, sending notification...');

      // Make sure we have all the necessary data for the notification
      const notificationData: any = {
        ...bookingData,
        id: bookingId,
        customer: {
          name: bookingData.customer?.name || 'Customer'
        }
      };

      // Try to send immediately first
      try {
        await sendNewBookingNotification(notificationData);
        console.log('Immediate notification sent successfully');
      } catch (immediateError) {
        console.error('Error in immediate notification sending:', immediateError);
      }

      // Use setTimeout to ensure the booking is fully saved before sending the notification again
      // This helps ensure that even if the first attempt fails, we have backup attempts
      setTimeout(async () => {
        try {
          // Create a more specific notification message for the delayed attempt
          const customerName = notificationData.customer?.name || 'Customer';
          let totalItems = 0;
          let totalPrice = 0;
          let bookingDays = 0;

          // Extract total items
          if (notificationData.products && Array.isArray(notificationData.products)) {
            totalItems = notificationData.products.length;
          }

          // Extract total price - check different possible properties
          if (notificationData.totalPrice && typeof notificationData.totalPrice === 'number') {
            totalPrice = notificationData.totalPrice;
          } else if (notificationData.total && typeof notificationData.total === 'number') {
            totalPrice = notificationData.total;
          } else if (notificationData.totalAmount && typeof notificationData.totalAmount === 'number') {
            totalPrice = notificationData.totalAmount;
          } else {
            // Try to calculate from products if available
            try {
              if (notificationData.products && Array.isArray(notificationData.products)) {
                totalPrice = notificationData.products.reduce((sum: number, product: any) => {
                  const price = product.price || product.totalPrice || 0;
                  const quantity = product.quantity || 1;
                  return sum + (price * quantity);
                }, 0);
              }
            } catch (error) {
              console.warn('Could not calculate total price from products:', error);
            }
          }

          // Extract booking days
          if (notificationData.rentalPeriod) {
            if (typeof notificationData.rentalPeriod.days === 'number') {
              bookingDays = notificationData.rentalPeriod.days;
            } else if (notificationData.rentalPeriod.dates && Array.isArray(notificationData.rentalPeriod.dates)) {
              bookingDays = notificationData.rentalPeriod.dates.length;
            }
          }

          // Format the price with 3 decimal places and currency symbol
          // Make sure totalPrice is a valid number to avoid NaN
          const validTotalPrice = isNaN(totalPrice) ? 0 : totalPrice;
          const formattedPrice = validTotalPrice.toFixed(3);

          // Create notification with the requested format
          const title = `New Booking - ${bookingDays} day${bookingDays !== 1 ? 's' : ''}`;
          const subject = `Quote: ${bookingId}`;
          const details = `${customerName}, booked: ${totalItems} item${totalItems !== 1 ? 's' : ''}, total: BD ${formattedPrice}`;
          const message = `${subject}\n${details}`;

          // Send a direct notification to the admin user
          await sendAdminDirectNotification(
            title,
            message,
            {
              bookingId: bookingId,
              type: 'booking_confirmation',
              timestamp: new Date().toISOString(),
              admin: true,
              delayed: true
            }
          );

          console.log('Delayed admin notification sent after booking creation');
        } catch (delayedError) {
          console.error('Error in delayed notification sending:', delayedError);
        }
      }, 2000);

    } catch (notificationError) {
      console.error('Error sending booking notification:', notificationError);
      // Don't throw this error as it shouldn't prevent the booking from being created
    }

    return bookingId;
  } catch (error) {
    console.error('Error adding booking:', error);
    throw error;
  }
};

export const updateBooking = async (booking: Booking): Promise<void> => {
  try {
    // Ensure all dates are valid but preserve the original booking date
    const bookingData = {
      ...booking,
      // Simplify product data to reduce database read/write operations
      products: booking.products.map(product => toBookingProduct(product)),
      // Keep the original booking date
      rentalPeriod: {
        ...booking.rentalPeriod,
        dates: Array.isArray(booking.rentalPeriod.dates)
          ? booking.rentalPeriod.dates.map(date => new Date(date).toISOString())
          : []
      }
    };

    // Remove any undefined values that would cause Firebase errors
    const cleanBookingData = JSON.parse(JSON.stringify(bookingData));

    const bookingRef = doc(db, 'bookings', booking.quoteNumber);

    // First check if the document exists
    const docSnap = await getDoc(bookingRef);

    if (!docSnap.exists()) {
      throw new Error(`Booking with quote number ${booking.quoteNumber} not found`);
    }

    // If document exists, update it
    // Keep the original date as a Timestamp
    await updateDoc(bookingRef, {
      ...cleanBookingData,
      // Preserve the original date format
      date: bookingData.date ? Timestamp.fromDate(new Date(bookingData.date)) : Timestamp.fromDate(new Date())
    });
  } catch (error) {
    console.error('Error updating booking:', error);
    throw error;
  }
};

export const deleteBooking = async (quoteNumber: string): Promise<void> => {
  try {
    const bookingRef = doc(db, 'bookings', quoteNumber);
    // First check if the document exists
    const docSnap = await getDoc(bookingRef);

    if (!docSnap.exists()) {
      throw new Error(`Booking with quote number ${quoteNumber} not found`);
    }

    await deleteDoc(bookingRef);
  } catch (error) {
    console.error('Error deleting booking:', error);
    throw error;
  }
};

// Coupons
export const getCoupons = async (): Promise<Coupon[]> => {
  const querySnapshot = await getDocs(collection(db, 'coupons'));
  return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as unknown as Coupon));
};

export const updateCoupon = async (coupon: Coupon): Promise<void> => {
  const couponRef = doc(db, 'coupons', coupon.id.toString());
  await setDoc(couponRef, coupon);
};

export const addCoupon = async (coupon: Omit<Coupon, 'id'>): Promise<string> => {
  const docRef = await addDoc(collection(db, 'coupons'), coupon);
  return docRef.id;
};

// Clients
export const getClients = async (): Promise<Client[]> => {
  const querySnapshot = await getDocs(collection(db, 'clients'));
  return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as unknown as Client));
};

export const updateClient = async (client: Client): Promise<void> => {
  try {
    // Normalize the email
    const normalizedEmail = client.email.toLowerCase().trim();
    const clientsRef = collection(db, 'clients');

    // Check if the email is being changed
    const isEmailChanged = client.id !== normalizedEmail && client.id.toLowerCase() !== normalizedEmail;

    // If the email is being changed
    if (isEmailChanged) {
      console.log('Email is being changed from', client.id, 'to', normalizedEmail);

      // Check if a client with the new email already exists
      const newEmailRef = doc(clientsRef, normalizedEmail);
      const newEmailDoc = await getDoc(newEmailRef);

      if (newEmailDoc.exists()) {
        throw new Error('A client with this email already exists');
      }

      // Get the old client document
      const oldClientRef = doc(clientsRef, client.id);
      const oldClientDoc = await getDoc(oldClientRef);

      if (!oldClientDoc.exists()) {
        throw new Error('Client not found');
      }

      // Create a batch to perform both operations atomically
      const batch = writeBatch(db);

      // Add the updated client with the new email as the document ID
      batch.set(newEmailRef, {
        ...client,
        email: normalizedEmail,
        id: normalizedEmail // Update the ID to match the document ID
      });

      // Delete the old client document
      batch.delete(oldClientRef);

      // Commit the batch
      await batch.commit();

      // Update client activities to point to the new client ID
      const activitiesRef = collection(db, 'clientActivities');
      const activitiesQuery = query(activitiesRef, where('clientId', '==', client.id));
      const activitiesSnapshot = await getDocs(activitiesQuery);

      // Update each activity in a batch if there are any
      if (!activitiesSnapshot.empty) {
        const activitiesBatch = writeBatch(db);

        activitiesSnapshot.forEach(doc => {
          activitiesBatch.update(doc.ref, { clientId: normalizedEmail });
        });

        await activitiesBatch.commit();
      }
    } else {
      // If the email is not being changed, just update the document
      // Use the client.id as the document ID since it might be an old format ID
      // or it might already be the normalized email
      const clientRef = doc(clientsRef, client.id);

      // Check if the client exists
      const clientDoc = await getDoc(clientRef);
      if (!clientDoc.exists()) {
        throw new Error('Client not found');
      }

      // Update the client document
      await setDoc(clientRef, {
        ...client,
        email: normalizedEmail
      });
    }
  } catch (error) {
    console.error('Error updating client:', error);
    throw error;
  }
};

export const addClient = async (client: Omit<Client, 'id'>): Promise<string> => {
  try {
    // Normalize the email to use as document ID
    const normalizedEmail = client.email.toLowerCase().trim();

    // Create a document reference with the email as the ID
    const clientsRef = collection(db, 'clients');
    const clientDocRef = doc(clientsRef, normalizedEmail);

    // Check if client with same email already exists
    const clientDoc = await getDoc(clientDocRef);

    if (clientDoc.exists()) {
      throw new Error('A client with this email already exists');
    }

    // If no duplicate found, add the new client using the email as the document ID
    await setDoc(clientDocRef, {
      ...client,
      email: normalizedEmail,
      dateAdded: Timestamp.now()
    });

    return normalizedEmail; // Return the email as the ID
  } catch (error) {
    console.error('Error adding client:', error);
    throw error;
  }
};

export const deleteClient = async (email: string): Promise<void> => {
  try {
    // Normalize the email
    const normalizedEmail = email.toLowerCase().trim();

    // Delete the client document directly using the email as the document ID
    const clientRef = doc(db, 'clients', normalizedEmail);

    // Check if the client exists first
    const clientDoc = await getDoc(clientRef);
    if (!clientDoc.exists()) {
      throw new Error('Client not found');
    }

    // Delete the client document
    await deleteDoc(clientRef);
  } catch (error) {
    console.error('Error deleting client:', error);
    throw error;
  }
};

export const mergeClients = async (primaryEmail: string, secondaryEmail: string): Promise<void> => {
  try {
    console.log('Starting merge in Firebase:', { primaryEmail, secondaryEmail });

    // Normalize emails
    const normalizedPrimaryEmail = primaryEmail.toLowerCase().trim();
    const normalizedSecondaryEmail = secondaryEmail.toLowerCase().trim();

    // Get both clients directly by their email IDs
    const clientsRef = collection(db, 'clients');
    const primaryRef = doc(clientsRef, normalizedPrimaryEmail);
    const secondaryRef = doc(clientsRef, normalizedSecondaryEmail);

    const [primaryDoc, secondaryDoc] = await Promise.all([
      getDoc(primaryRef),
      getDoc(secondaryRef)
    ]);

    if (!primaryDoc.exists() || !secondaryDoc.exists()) {
      console.error('One or both clients not found:', {
        primaryFound: primaryDoc.exists(),
        secondaryFound: secondaryDoc.exists()
      });
      throw new Error('One or both clients not found');
    }

    const primaryClient = primaryDoc.data() as Client;
    const secondaryClient = secondaryDoc.data() as Client;

    console.log('Found clients to merge:', {
      primary: { ...primaryClient, firebaseId: normalizedPrimaryEmail },
      secondary: { ...secondaryClient, firebaseId: normalizedSecondaryEmail }
    });

    // Get all activities for both clients
    const activitiesRef = collection(db, 'clientActivities');
    const [primaryActivities, secondaryActivities] = await Promise.all([
      getDocs(query(activitiesRef, where('clientId', '==', String(primaryClient.id)))),
      getDocs(query(activitiesRef, where('clientId', '==', String(secondaryClient.id))))
    ]);

    // Update secondary client's activities to point to primary client
    const updatePromises = secondaryActivities.docs.map(doc => {
      const activity = doc.data() as ClientActivity;
      return setDoc(doc.ref, {
        ...activity,
        clientId: String(primaryClient.id)
      });
    });

    // Merge client data
    const mergedClient: Client = {
      ...primaryClient,
      totalBookings: (primaryClient.totalBookings || 0) + (secondaryClient.totalBookings || 0),
      totalSpent: (primaryClient.totalSpent || 0) + (secondaryClient.totalSpent || 0),
      pendingPayment: (primaryClient.pendingPayment || 0) + (secondaryClient.pendingPayment || 0),
      notes: primaryClient.notes
        ? `${primaryClient.notes}\n\nMerged with ${secondaryClient.name} (${secondaryClient.email}) on ${new Date().toLocaleDateString()}`
        : `Merged with ${secondaryClient.name} (${secondaryClient.email}) on ${new Date().toLocaleDateString()}`
    };

    console.log('Merged client data:', mergedClient);

    // Create a merge activity record
    const mergeActivity: Omit<ClientActivity, 'id'> = {
      clientId: String(primaryClient.id),
      date: new Date().toISOString(),
      type: 'merge',
      description: `Merged client ${secondaryClient.name} (${secondaryClient.email}) into ${primaryClient.name} (${primaryClient.email})`,
      amount: secondaryClient.totalSpent || 0
    };

    // Execute all updates in parallel
    await Promise.all([
      setDoc(primaryRef, mergedClient),
      addClientActivity(mergeActivity),
      ...updatePromises,
      deleteDoc(secondaryRef)
    ]);

    console.log('Merge completed in Firebase');
  } catch (error) {
    console.error('Error merging clients:', error);
    throw error;
  }
};

// Client Activities
export const getClientActivities = async (clientId: string): Promise<ClientActivity[]> => {
  try {
    const activitiesRef = collection(db, 'clientActivities');
    let q = query(activitiesRef);

    if (clientId) {
      q = query(activitiesRef, where('clientId', '==', clientId));
    }

    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as unknown as ClientActivity));
  } catch (error) {
    console.error('Error fetching client activities:', error);
    return [];
  }
};

export const addClientActivity = async (activity: Omit<ClientActivity, 'id'>): Promise<string> => {
  const docRef = await addDoc(collection(db, 'clientActivities'), {
    ...activity,
    date: Timestamp.fromDate(new Date(activity.date))
  });
  return docRef.id;
};

// System Settings
export const getSystemSettings = async (): Promise<SystemSettings> => {
  try {
    // Try to get settings from 'general' document first
    const settingsRef = doc(db, 'systemSettings', 'general');
    const settingsDoc = await getDoc(settingsRef);

    // Also try to get settings from 'settings' document for the lastQuoteNumber
    const backupSettingsRef = doc(db, 'systemSettings', 'settings');
    const backupSettingsDoc = await getDoc(backupSettingsRef);

    if (settingsDoc.exists()) {
      const generalSettings = settingsDoc.data() as SystemSettings;

      // If backup settings exist and have a lastQuoteNumber that's higher, use that
      if (backupSettingsDoc.exists()) {
        const backupSettings = backupSettingsDoc.data() as SystemSettings;

        // Ensure lastQuoteNumber is present and use the highest value between the two documents
        const generalQuoteNumber = generalSettings.lastQuoteNumber || 1000;
        const backupQuoteNumber = backupSettings.lastQuoteNumber || 1000;

        generalSettings.lastQuoteNumber = Math.max(generalQuoteNumber, backupQuoteNumber);
      }

      return generalSettings;
    } else if (backupSettingsDoc.exists()) {
      // If only backup settings exist, use those
      return backupSettingsDoc.data() as SystemSettings;
    }

    return initialSystemSettings;
  } catch (error) {
    console.error('Error fetching system settings:', error);
    return initialSystemSettings;
  }
};

export const updateSystemSettings = async (settings: SystemSettings): Promise<void> => {
  try {
    // First, get the current settings to preserve lastQuoteNumber if it's not provided
    let currentSettings: SystemSettings = initialSystemSettings;
    try {
      const settingsRef = doc(db, 'systemSettings', 'general');
      const settingsDoc = await getDoc(settingsRef);
      if (settingsDoc.exists()) {
        currentSettings = settingsDoc.data() as SystemSettings;
      }
    } catch (innerError) {
      console.warn('Could not fetch current settings, using defaults:', innerError);
    }

    // Ensure we keep the lastQuoteNumber from current settings if not specified in the new settings
    const lastQuoteNumber = settings.lastQuoteNumber ?? currentSettings.lastQuoteNumber ?? 1000;

    // Create a clean copy of the settings with all required fields
    const cleanSettings: SystemSettings = {
      // Ensure taxRate is a number
      taxRate: typeof settings.taxRate === 'string' ? parseFloat(settings.taxRate) : settings.taxRate,
      enableTax: settings.enableTax ?? currentSettings.enableTax ?? false,
      deliveryOptions: settings.deliveryOptions ?? currentSettings.deliveryOptions ?? [],
      lastQuoteNumber: lastQuoteNumber
    };

    // Update the 'general' document
    const settingsRef = doc(db, 'systemSettings', 'general');
    await setDoc(settingsRef, cleanSettings);

    // Also update the 'settings' document as a backup
    const backupSettingsRef = doc(db, 'systemSettings', 'settings');
    await setDoc(backupSettingsRef, cleanSettings);

    console.log('Successfully updated system settings with lastQuoteNumber:', lastQuoteNumber);
  } catch (error) {
    console.error('Error updating system settings:', error);
    throw error;
  }
};

// Categories
export const getCategories = async (): Promise<string[]> => {
  try {
    const categoriesRef = collection(db, 'categories');
    const querySnapshot = await getDocs(categoriesRef);
    return querySnapshot.docs.map(doc => doc.data().name);
  } catch (error) {
    console.error('Error getting categories:', error);
    throw error;
  }
};

export const addCategory = async (categoryName: string): Promise<void> => {
  try {
    if (!categoryName) {
      throw new Error('Category name is required');
    }

    // Check if category already exists
    const categoriesRef = collection(db, 'categories');
    const q = query(categoriesRef, where('name', '==', categoryName.toLowerCase()));
    const querySnapshot = await getDocs(q);

    if (!querySnapshot.empty) {
      throw new Error('A category with this name already exists');
    }

    // Add the new category
    await addDoc(categoriesRef, {
      name: categoryName.toLowerCase(),
      createdAt: Timestamp.now()
    });
  } catch (error) {
    console.error('Error adding category:', error);
    throw error;
  }
};

export const deleteCategory = async (categoryName: string): Promise<void> => {
  try {
    if (!categoryName) {
      throw new Error('Category name is required');
    }

    // Find the category by name
    const categoriesRef = collection(db, 'categories');
    const q = query(categoriesRef, where('name', '==', categoryName.toLowerCase()));
    const querySnapshot = await getDocs(q);

    if (querySnapshot.empty) {
      throw new Error('Category not found');
    }

    // Delete the category
    const categoryDoc = querySnapshot.docs[0];
    await deleteDoc(categoryDoc.ref);

    // Update all products in this category to 'Uncategorized'
    const productsRef = collection(db, 'products');
    const productsQuery = query(productsRef, where('category', '==', categoryName.toLowerCase()));
    const productsSnapshot = await getDocs(productsQuery);

    const batch = writeBatch(db);
    productsSnapshot.docs.forEach(doc => {
      batch.update(doc.ref, { category: 'uncategorized' });
    });
    await batch.commit();
  } catch (error) {
    console.error('Error deleting category:', error);
    throw error;
  }
};

// Users
export const getUsers = async (): Promise<User[]> => {
  try {
    const querySnapshot = await getDocs(collection(db, 'users'));
    return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as User));
  } catch (error) {
    console.error('Error getting users:', error);
    return [];
  }
};

export const getUserByEmail = async (email: string): Promise<User | null> => {
  try {
    const usersRef = collection(db, 'users');
    const q = query(usersRef, where('email', '==', email.toLowerCase()));
    const querySnapshot = await getDocs(q);

    if (querySnapshot.empty) {
      return null;
    }

    return { id: querySnapshot.docs[0].id, ...querySnapshot.docs[0].data() } as User;
  } catch (error) {
    console.error('Error getting user by email:', error);
    return null;
  }
};

export const getUserByUsername = async (username: string): Promise<User | null> => {
  try {
    const usersRef = collection(db, 'users');
    const q = query(usersRef, where('username', '==', username));
    const querySnapshot = await getDocs(q);

    if (querySnapshot.empty) {
      return null;
    }

    return { id: querySnapshot.docs[0].id, ...querySnapshot.docs[0].data() } as User;
  } catch (error) {
    console.error('Error getting user by username:', error);
    return null;
  }
};

export const updateUserLastLogin = async (userId: string): Promise<void> => {
  try {
    const userRef = doc(db, 'users', userId);
    await updateDoc(userRef, {
      lastLogin: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error updating user last login:', error);
  }
};

export const addUser = async (user: Omit<User, 'id'>): Promise<string> => {
  try {
    // Check if user with same email already exists
    const existingUserEmail = await getUserByEmail(user.email);
    if (existingUserEmail) {
      throw new Error('A user with this email already exists');
    }

    // Check if user with same username already exists
    const existingUsername = await getUserByUsername(user.username);
    if (existingUsername) {
      throw new Error('A user with this username already exists');
    }

    // Add the new user
    const docRef = await addDoc(collection(db, 'users'), {
      ...user,
      email: user.email.toLowerCase(),
      createdAt: new Date().toISOString(),
      isActive: true
    });

    return docRef.id;
  } catch (error) {
    console.error('Error adding user:', error);
    throw error;
  }
};

export const updateUser = async (user: User): Promise<void> => {
  try {
    // Check if username is being changed and if it already exists
    const currentUser = await getDoc(doc(db, 'users', user.id));
    if (currentUser.exists()) {
      const currentData = currentUser.data() as User;
      if (currentData.username !== user.username) {
        const existingUsername = await getUserByUsername(user.username);
        if (existingUsername && existingUsername.id !== user.id) {
          throw new Error('A user with this username already exists');
        }
      }
    }

    const userRef = doc(db, 'users', user.id);
    await updateDoc(userRef, {
      ...user,
      email: user.email.toLowerCase()
    });
  } catch (error) {
    console.error('Error updating user:', error);
    throw error;
  }
};

export const deleteUser = async (userId: string): Promise<void> => {
  try {
    const userRef = doc(db, 'users', userId);
    await deleteDoc(userRef);
  } catch (error) {
    console.error('Error deleting user:', error);
    throw error;
  }
};

export const authenticateUser = async (username: string, password: string): Promise<User | null> => {
  try {
    // For simplicity, we're storing the password directly in the user document
    // In a real application, you would use Firebase Authentication or implement proper password hashing
    const usersRef = collection(db, 'users');

    // Try to match by username first
    let q = query(
      usersRef,
      where('username', '==', username),
      where('password', '==', password),
      where('isActive', '==', true)
    );

    let querySnapshot = await getDocs(q);

    // If no match by username, try email
    if (querySnapshot.empty) {
      q = query(
        usersRef,
        where('email', '==', username.toLowerCase()),
        where('password', '==', password),
        where('isActive', '==', true)
      );

      querySnapshot = await getDocs(q);
    }

    if (querySnapshot.empty) {
      return null;
    }

    const user = { id: querySnapshot.docs[0].id, ...querySnapshot.docs[0].data() } as User & { password: string };

    // Update last login
    await updateUserLastLogin(user.id);

    // Don't return the password
    const { password: _, ...userWithoutPassword } = user;

    return userWithoutPassword;
  } catch (error) {
    console.error('Error authenticating user:', error);
    return null;
  }
};

// Vendor Management

// Get all vendors
export const getVendors = async (): Promise<Vendor[]> => {
  try {
    const vendorsRef = collection(db, 'vendors');
    const querySnapshot = await getDocs(query(vendorsRef, orderBy('name')));

    return querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        ...data,
        id: doc.id,
        createdAt: data.createdAt?.toDate?.()?.toISOString() || new Date().toISOString()
      } as Vendor;
    });
  } catch (error) {
    console.error('Error getting vendors:', error);
    throw error;
  }
};

// Add a new vendor
export const addVendor = async (vendor: Omit<Vendor, 'id' | 'createdAt'>): Promise<string> => {
  try {
    const vendorsRef = collection(db, 'vendors');
    const newVendorRef = await addDoc(vendorsRef, {
      ...vendor,
      createdAt: serverTimestamp()
    });

    return newVendorRef.id;
  } catch (error) {
    console.error('Error adding vendor:', error);
    throw error;
  }
};

// Update an existing vendor
export const updateVendor = async (vendor: Vendor): Promise<void> => {
  try {
    const vendorRef = doc(db, 'vendors', vendor.id);

    // Remove id from the data to be updated
    const { id, ...vendorData } = vendor;

    await updateDoc(vendorRef, vendorData);
  } catch (error) {
    console.error('Error updating vendor:', error);
    throw error;
  }
};

// Delete a vendor
export const deleteVendor = async (vendorId: string): Promise<void> => {
  try {
    // Check if vendor has associated products
    const productsRef = collection(db, 'products');
    const productsQuery = query(productsRef, where('vendorId', '==', vendorId));
    const productSnapshot = await getDocs(productsQuery);

    if (!productSnapshot.empty) {
      throw new Error('Cannot delete vendor with associated products. Please reassign or remove the products first.');
    }

    // Delete the vendor
    const vendorRef = doc(db, 'vendors', vendorId);
    await deleteDoc(vendorRef);
  } catch (error) {
    console.error('Error deleting vendor:', error);
    throw error;
  }
};

// Get a specific vendor by ID
export const getVendorById = async (vendorId: string): Promise<Vendor | null> => {
  try {
    const vendorRef = doc(db, 'vendors', vendorId);
    const vendorSnap = await getDoc(vendorRef);

    if (!vendorSnap.exists()) {
      return null;
    }

    const data = vendorSnap.data();
    return {
      ...data,
      id: vendorSnap.id,
      createdAt: data.createdAt?.toDate?.()?.toISOString() || new Date().toISOString()
    } as Vendor;
  } catch (error) {
    console.error('Error getting vendor by ID:', error);
    throw error;
  }
};

// Vendor Transactions

// Get all transactions for a vendor
export const getVendorTransactions = async (vendorId: string): Promise<VendorTransaction[]> => {
  try {
    const transactionsRef = collection(db, 'vendorTransactions');
    const q = query(
      transactionsRef,
      where('vendorId', '==', vendorId),
      orderBy('date', 'desc')
    );

    const querySnapshot = await getDocs(q);

    return querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        ...data,
        id: doc.id,
        date: data.date?.toDate?.()?.toISOString() || new Date().toISOString()
      } as VendorTransaction;
    });
  } catch (error) {
    console.error('Error getting vendor transactions:', error);
    throw error;
  }
};

// Add a new transaction
export const addVendorTransaction = async (transaction: Omit<VendorTransaction, 'id'>): Promise<string> => {
  try {
    const transactionsRef = collection(db, 'vendorTransactions');
    const newTransactionRef = await addDoc(transactionsRef, {
      ...transaction,
      date: Timestamp.fromDate(new Date(transaction.date))
    });

    return newTransactionRef.id;
  } catch (error) {
    console.error('Error adding vendor transaction:', error);
    throw error;
  }
};

// Update a transaction
export const updateVendorTransaction = async (transaction: VendorTransaction): Promise<void> => {
  try {
    const transactionRef = doc(db, 'vendorTransactions', transaction.id);

    // Remove id from the data to be updated
    const { id, ...transactionData } = transaction;

    // Convert date string to Timestamp
    const updatedData = {
      ...transactionData,
      date: Timestamp.fromDate(new Date(transaction.date))
    };

    await updateDoc(transactionRef, updatedData);
  } catch (error) {
    console.error('Error updating vendor transaction:', error);
    throw error;
  }
};

// Delete a transaction
export const deleteVendorTransaction = async (transactionId: string): Promise<void> => {
  try {
    const transactionRef = doc(db, 'vendorTransactions', transactionId);
    await deleteDoc(transactionRef);
  } catch (error) {
    console.error('Error deleting vendor transaction:', error);
    throw error;
  }
};

// Get products by vendor
export const getProductsByVendor = async (vendorId: string): Promise<Product[]> => {
  try {
    const productsRef = collection(db, 'products');
    const q = query(productsRef, where('vendorId', '==', vendorId));
    const querySnapshot = await getDocs(q);

    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    } as Product));
  } catch (error) {
    console.error('Error getting products by vendor:', error);
    throw error;
  }
};

// Get vendor's revenue data (from bookings with vendor products)
export const getVendorRevenueData = async (vendorId: string, startDate: Date, endDate: Date) => {
  try {
    // Get vendor's products
    const vendorProducts = await getProductsByVendor(vendorId);
    const vendorProductIds = vendorProducts.map(product => product.id);

    // If no products, return empty data
    if (vendorProductIds.length === 0) {
      return {
        totalRevenue: 0,
        totalCost: 0,
        totalProfit: 0,
        bookings: []
      };
    }

    // Get bookings in date range
    const bookingsRef = collection(db, 'bookings');
    const q = query(
      bookingsRef,
      where('date', '>=', Timestamp.fromDate(startDate)),
      where('date', '<=', Timestamp.fromDate(endDate))
    );

    const querySnapshot = await getDocs(q);

    let totalRevenue = 0;
    let totalCost = 0;
    let totalProfit = 0;
    const bookingsWithVendorItems = [];

    for (const bookingDoc of querySnapshot.docs) {
      const booking = {
        id: bookingDoc.id,
        ...bookingDoc.data()
      } as Booking;

      // Check if booking has any vendor products
      const vendorProductsInBooking = booking.products.filter(
        product => vendorProductIds.includes(product.id)
      );

      if (vendorProductsInBooking.length > 0) {
        // Calculate revenue, cost and profit
        let bookingRevenue = 0;
        let bookingCost = 0;

        vendorProductsInBooking.forEach(product => {
          const fullProduct = vendorProducts.find(p => p.id === product.id);
          if (fullProduct) {
            const days = product.customDays || booking.rentalPeriod.days;
            const dailyRate = product.temporaryDailyRate || fullProduct.dailyRate;
            const quantity = product.quantity;

            const revenue = dailyRate * days * quantity;
            const cost = (fullProduct.vendorCost || 0) * days * quantity;

            bookingRevenue += revenue;
            bookingCost += cost;
          }
        });

        const bookingProfit = bookingRevenue - bookingCost;

        totalRevenue += bookingRevenue;
        totalCost += bookingCost;
        totalProfit += bookingProfit;

        bookingsWithVendorItems.push({
          booking,
          vendorProducts: vendorProductsInBooking,
          revenue: bookingRevenue,
          cost: bookingCost,
          profit: bookingProfit
        });
      }
    }

    return {
      totalRevenue,
      totalCost,
      totalProfit,
      bookings: bookingsWithVendorItems
    };
  } catch (error) {
    console.error('Error getting vendor revenue data:', error);
    throw error;
  }
};