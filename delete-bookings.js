import { initializeApp } from 'firebase/app';
import { getFirestore, collection, getDocs, deleteDoc, doc } from 'firebase/firestore';
import dotenv from 'dotenv';
import { dirname } from 'path';
import { fileURLToPath } from 'url';

// Initialize dotenv
dotenv.config();

// Firebase configuration
const firebaseConfig = {
  apiKey: process.env.VITE_FIREBASE_API_KEY,
  authDomain: process.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: process.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.VITE_FIREBASE_APP_ID
};

console.log('Firebase Config:', {
  ...firebaseConfig,
  apiKey: firebaseConfig.apiKey ? '***' : undefined
});

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

async function deleteAllBookings() {
  try {
    console.log('Fetching all bookings...');
    const querySnapshot = await getDocs(collection(db, 'bookings'));
    console.log(`Found ${querySnapshot.size} bookings to delete`);
    
    for (const docSnapshot of querySnapshot.docs) {
      const quoteNumber = docSnapshot.id;
      console.log(`Deleting booking with quote number: ${quoteNumber}`);
      await deleteDoc(doc(db, 'bookings', quoteNumber));
    }
    
    console.log('All bookings deleted successfully');
  } catch (error) {
    console.error('Error deleting bookings:', error);
  }
}

deleteAllBookings(); 