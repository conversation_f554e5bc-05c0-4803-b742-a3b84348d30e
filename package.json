{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "concurrently \"npm run server\" \"vite\"", "server": "node server/index.js", "client": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "setup-db": "node scripts/initDb.js", "seed-data": "node scripts/seedData.js"}, "dependencies": {"@fullcalendar/daygrid": "^6.1.17", "@fullcalendar/interaction": "^6.1.17", "@fullcalendar/list": "^6.1.17", "@fullcalendar/react": "^6.1.17", "@fullcalendar/timegrid": "^6.1.17", "@react-pdf/renderer": "^4.3.0", "axios": "^1.8.4", "chart.js": "^4.4.8", "concurrently": "^9.2.0", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "express": "^4.21.2", "html-to-image": "^1.11.13", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.344.0", "mysql2": "^3.14.1", "react": "^18.3.1", "react-big-calendar": "^1.18.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.1", "react-draggable": "^4.4.6", "react-grid-layout": "^1.5.1", "react-resizable": "^3.0.5"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/node": "^22.14.0", "@types/react": "^18.3.5", "@types/react-big-calendar": "^1.16.1", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "netlify-cli": "^19.1.3", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "typescript": "^5.8.2", "typescript-eslint": "^8.3.0", "vite": "^6.2.3"}}